<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'printing_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('SYSTEM_NAME', 'نظام إدارة الطباعة');
define('SYSTEM_VERSION', '2.0');
define('API_VERSION', 'v1');

// إعدادات الأمان
define('API_KEY', 'printing_system_2024');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// إعدادات الشبكة
define('ALLOW_CORS', true);
define('MAX_CONNECTIONS', 50);

// مسارات النظام
define('BASE_PATH', dirname(__FILE__));
define('API_PATH', BASE_PATH . '/api/');
define('UPLOADS_PATH', BASE_PATH . '/uploads/');

// إعدادات التاريخ والوقت
date_default_timezone_set('Asia/Riyadh');

// دالة الاتصال بقاعدة البيانات
function getDBConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        return false;
    }
}

// دالة إرسال استجابة JSON
function sendResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json; charset=utf-8');
    if (ALLOW_CORS) {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');
    }
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// دالة إرسال خطأ
function sendError($message, $code = 400) {
    sendResponse([
        'success' => false,
        'error' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ], $code);
}

// دالة إرسال نجاح
function sendSuccess($data = null, $message = 'تم بنجاح') {
    sendResponse([
        'success' => true,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

// دالة التحقق من API Key
function validateApiKey() {
    $headers = getallheaders();
    $apiKey = $headers['X-API-Key'] ?? $_GET['api_key'] ?? null;
    
    if ($apiKey !== API_KEY) {
        sendError('مفتاح API غير صحيح', 401);
    }
}

// دالة تسجيل الأنشطة
function logActivity($action, $details = null) {
    try {
        $pdo = getDBConnection();
        if ($pdo) {
            $stmt = $pdo->prepare("INSERT INTO activity_log (action, details, ip_address, created_at) VALUES (?, ?, ?, NOW())");
            $stmt->execute([$action, json_encode($details, JSON_UNESCAPED_UNICODE), $_SERVER['REMOTE_ADDR']]);
        }
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}
?>