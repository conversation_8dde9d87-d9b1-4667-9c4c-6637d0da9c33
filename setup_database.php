<?php
// ملف إعداد قاعدة البيانات
error_reporting(E_ALL);
ini_set('display_errors', 1);

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'printing_system';

echo "<h2>🔧 إعداد قاعدة البيانات - نظام إدارة الطباعة</h2>";
echo "<hr>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    echo "📡 محاولة الاتصال بـ MySQL...<br>";
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    echo "✅ تم الاتصال بـ MySQL بنجاح!<br><br>";

    // إنشاء قاعدة البيانات
    echo "🗄️ إنشاء قاعدة البيانات...<br>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات '$database' بنجاح!<br><br>";

    // الاتصال بقاعدة البيانات المحددة
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    // قراءة ملف SQL وتنفيذه
    echo "📋 قراءة ملف قاعدة البيانات...<br>";
    $sql = file_get_contents('database.sql');
    
    if ($sql === false) {
        throw new Exception("لا يمكن قراءة ملف database.sql");
    }

    // تقسيم الاستعلامات
    $queries = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "🔄 تنفيذ الاستعلامات...<br>";
    $success_count = 0;
    $error_count = 0;

    foreach ($queries as $query) {
        if (empty($query) || strpos($query, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($query);
            $success_count++;
            echo "✅ تم تنفيذ استعلام بنجاح<br>";
        } catch (PDOException $e) {
            $error_count++;
            echo "❌ خطأ في الاستعلام: " . $e->getMessage() . "<br>";
        }
    }

    echo "<br><h3>📊 ملخص التثبيت:</h3>";
    echo "✅ الاستعلامات الناجحة: $success_count<br>";
    echo "❌ الاستعلامات الفاشلة: $error_count<br><br>";

    // التحقق من الجداول المنشأة
    echo "<h3>📋 الجداول المنشأة:</h3>";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "❌ لم يتم إنشاء أي جداول!<br>";
    } else {
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>✅ $table</li>";
        }
        echo "</ul>";
    }

    // إدراج بيانات تجريبية
    echo "<br><h3>🎯 إدراج بيانات تجريبية...</h3>";
    
    // إضافة طابعين
    try {
        $pdo->exec("INSERT IGNORE INTO printers (name, description) VALUES 
                   ('أحمد محمد', 'طباع رئيسي'),
                   ('محمد علي', 'طباع مساعد'),
                   ('سعد أحمد', 'طباع متخصص')");
        echo "✅ تم إضافة الطابعين<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في إضافة الطابعين: " . $e->getMessage() . "<br>";
    }

    // إضافة عملاء تجريبيين
    try {
        $pdo->exec("INSERT IGNORE INTO customers (name, type, phone, balance) VALUES 
                   ('شركة الأمل', 'customer', '0501234567', 0),
                   ('مؤسسة النور', 'customer', '0507654321', 0),
                   ('شركة الورق', 'supplier', '0551234567', 0)");
        echo "✅ تم إضافة العملاء التجريبيين<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في إضافة العملاء: " . $e->getMessage() . "<br>";
    }

    echo "<br><h2>🎉 تم إعداد قاعدة البيانات بنجاح!</h2>";
    echo "<p><strong>يمكنك الآن استخدام النظام بكامل ميزاته.</strong></p>";
    echo "<p><a href='index.html' style='background: #10b981; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 انتقل إلى النظام</a></p>";

} catch (Exception $e) {
    echo "<h2>❌ خطأ في إعداد قاعدة البيانات</h2>";
    echo "<p style='color: red;'><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>تأكد من:</strong></p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "<li>وجود ملف database.sql</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    color: #333;
    direction: rtl;
}

div, h2, h3, p, ul, li {
    background: white;
    margin: 10px 0;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h2 {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    text-align: center;
}

h3 {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}
</style>
