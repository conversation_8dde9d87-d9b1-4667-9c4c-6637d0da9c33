<?php
// اختبار الاتصال بقاعدة البيانات
require_once 'config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $pdo = getDBConnection();
    
    if ($pdo) {
        // اختبار الجداول
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        // عد البيانات في كل جدول
        $data = [];
        foreach ($tables as $table) {
            try {
                $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
                $data[$table] = $count;
            } catch (Exception $e) {
                $data[$table] = 'خطأ: ' . $e->getMessage();
            }
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'الاتصال بقاعدة البيانات ناجح',
            'tables' => $tables,
            'data_count' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'فشل الاتصال بقاعدة البيانات',
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
