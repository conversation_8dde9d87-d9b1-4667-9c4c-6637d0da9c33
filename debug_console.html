<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وحدة تحكم التشخيص - نظام إدارة الطباعة</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Arial, sans-serif;
            background: #1e293b;
            margin: 0;
            padding: 20px;
            color: #f1f5f9;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        h1 {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        .console {
            background: #0f172a;
            border: 2px solid #334155;
            border-radius: 12px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .btn {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn.danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        .btn.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .status {
            background: #374151;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .back-btn {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 وحدة تحكم التشخيص</h1>
        
        <div class="status" id="status">
            <strong>حالة النظام:</strong> جاري التحقق...
        </div>

        <div class="controls">
            <button class="btn" onclick="checkSettings()">فحص الإعدادات</button>
            <button class="btn" onclick="testWhatsApp()">اختبار WhatsApp</button>
            <button class="btn" onclick="forceReload()">إعادة تحميل الإعدادات</button>
            <button class="btn danger" onclick="clearAll()">مسح جميع البيانات</button>
            <button class="btn success" onclick="fixSettings()">إصلاح الإعدادات</button>
        </div>

        <div class="console" id="console">
            <div style="color: #10b981;">🚀 وحدة تحكم التشخيص جاهزة...</div>
        </div>

        <div style="text-align: center;">
            <a href="index.html" class="back-btn">🔙 العودة للنظام</a>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: '#60a5fa',
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b'
            };
            
            const div = document.createElement('div');
            div.style.color = colors[type] || colors.info;
            div.innerHTML = `[${timestamp}] ${message}`;
            console.appendChild(div);
            console.scrollTop = console.scrollHeight;
        }

        function updateStatus(message) {
            document.getElementById('status').innerHTML = `<strong>حالة النظام:</strong> ${message}`;
        }

        function checkSettings() {
            log('🔍 فحص إعدادات النظام...', 'info');
            
            try {
                const saved = localStorage.getItem('modernPrintingSettings');
                if (saved) {
                    const settings = JSON.parse(saved);
                    log(`✅ تم العثور على إعدادات محفوظة`, 'success');
                    log(`📱 طريقة WhatsApp: ${settings.whatsappOpenMethod || 'غير محدد'}`, 'info');
                    log(`🏢 اسم الشركة: ${settings.companyName || 'غير محدد'}`, 'info');
                    log(`📊 حجم البيانات: ${JSON.stringify(settings).length} حرف`, 'info');
                    
                    if (!settings.whatsappOpenMethod) {
                        log('⚠️ إعداد WhatsApp مفقود!', 'warning');
                        updateStatus('إعداد WhatsApp مفقود - يحتاج إصلاح');
                    } else {
                        updateStatus('الإعدادات سليمة');
                    }
                } else {
                    log('❌ لا توجد إعدادات محفوظة', 'error');
                    updateStatus('لا توجد إعدادات محفوظة');
                }
            } catch (error) {
                log(`❌ خطأ في قراءة الإعدادات: ${error.message}`, 'error');
                updateStatus('خطأ في قراءة الإعدادات');
            }
        }

        function testWhatsApp() {
            log('🧪 اختبار فتح WhatsApp...', 'info');
            
            const testMessage = 'اختبار من وحدة التحكم';
            const methods = ['app', 'web', 'smart'];
            
            methods.forEach(method => {
                log(`📱 اختبار طريقة: ${method}`, 'info');
                
                if (method === 'app') {
                    const appUrl = `whatsapp://send?text=${encodeURIComponent(testMessage)}`;
                    try {
                        window.location.href = appUrl;
                        log(`✅ تم إرسال أمر فتح التطبيق`, 'success');
                    } catch (error) {
                        log(`❌ فشل فتح التطبيق: ${error.message}`, 'error');
                    }
                } else if (method === 'web') {
                    const webUrl = `https://wa.me/?text=${encodeURIComponent(testMessage)}`;
                    try {
                        window.open(webUrl, '_blank');
                        log(`✅ تم فتح WhatsApp Web`, 'success');
                    } catch (error) {
                        log(`❌ فشل فتح الويب: ${error.message}`, 'error');
                    }
                }
            });
        }

        function forceReload() {
            log('🔄 إعادة تحميل الإعدادات...', 'info');
            
            try {
                const defaultSettings = {
                    companyName: 'مطبعة الحديثة',
                    companyPhone: '*********',
                    companyAddress: 'صنعاء - اليمن',
                    whatsappOpenMethod: 'smart',
                    whatsappStyle: 'modern',
                    whatsappHeader: '🖨️ *فاتورة طباعة*',
                    whatsappFooter: '🙏 شكراً لثقتكم بنا'
                };
                
                const saved = localStorage.getItem('modernPrintingSettings');
                let settings = saved ? JSON.parse(saved) : {};
                
                // دمج الإعدادات
                settings = { ...defaultSettings, ...settings };
                
                // التأكد من وجود إعداد WhatsApp
                if (!settings.whatsappOpenMethod) {
                    settings.whatsappOpenMethod = 'smart';
                }
                
                localStorage.setItem('modernPrintingSettings', JSON.stringify(settings));
                log('✅ تم إعادة تحميل الإعدادات بنجاح', 'success');
                log(`📱 طريقة WhatsApp: ${settings.whatsappOpenMethod}`, 'info');
                updateStatus('تم إعادة تحميل الإعدادات');
                
            } catch (error) {
                log(`❌ خطأ في إعادة التحميل: ${error.message}`, 'error');
                updateStatus('خطأ في إعادة التحميل');
            }
        }

        function clearAll() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                log('🗑️ مسح جميع البيانات...', 'warning');
                
                try {
                    localStorage.clear();
                    log('✅ تم مسح جميع البيانات', 'success');
                    updateStatus('تم مسح جميع البيانات');
                } catch (error) {
                    log(`❌ خطأ في المسح: ${error.message}`, 'error');
                }
            }
        }

        function fixSettings() {
            log('🔧 إصلاح الإعدادات...', 'info');
            
            try {
                const fixedSettings = {
                    companyName: 'مطبعة الحديثة',
                    companyPhone: '*********',
                    companyAddress: 'صنعاء - اليمن',
                    companyEmail: '<EMAIL>',
                    companyWebsite: 'www.modernprint.ye',
                    companyLogo: '🖨️',
                    defaultCurrency: 'ريال يمني',
                    invoiceTheme: 'modern',
                    invoiceColor: '#3b82f6',
                    invoiceHeaderStyle: 'gradient',
                    showLogo: true,
                    showBorder: true,
                    showFooterMessage: true,
                    footerMessage: '🙏 شكراً لثقتكم بنا',
                    showCompanyDetails: true,
                    hideFromWhatsApp: ['printerName'],
                    whatsappStyle: 'modern',
                    whatsappHeader: '🖨️ *فاتورة طباعة*',
                    whatsappFooter: '🙏 شكراً لثقتكم بنا',
                    showCompanyInfoInWhatsApp: true,
                    showBordersInWhatsApp: true,
                    whatsappOpenMethod: 'app', // تعيين التطبيق فقط
                    dateFormat: 'en-US',
                    numberFormat: 'english'
                };
                
                localStorage.setItem('modernPrintingSettings', JSON.stringify(fixedSettings));
                log('✅ تم إصلاح جميع الإعدادات', 'success');
                log('📱 تم تعيين طريقة WhatsApp إلى "التطبيق فقط"', 'success');
                updateStatus('تم إصلاح الإعدادات - طريقة التطبيق فقط مفعلة');
                
            } catch (error) {
                log(`❌ خطأ في الإصلاح: ${error.message}`, 'error');
                updateStatus('خطأ في إصلاح الإعدادات');
            }
        }

        // تشغيل فحص أولي
        window.onload = function() {
            setTimeout(checkSettings, 1000);
        };
    </script>
</body>
</html>
