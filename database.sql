-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS printing_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE printing_system;

-- جدول الأعمال
CREATE TABLE jobs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    job_name VARCHAR(255) NOT NULL,
    job_details TEXT,
    quantity INT DEFAULT 0,
    printer_name VARCHAR(100),
    invoice_number VARCHAR(50),
    customer_name VARCHAR(255),
    amount DECIMAL(10,2) DEFAULT 0.00,
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_customer (customer_name),
    INDEX idx_invoice (invoice_number),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
);

-- جدو<PERSON> العملاء والموردين
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL UNIQUE,
    type ENUM('customer', 'supplier') NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    balance DECIMAL(10,2) DEFAULT 0.00,
    total_jobs INT DEFAULT 0,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_balance (balance)
);

-- جدول الإيصالات
CREATE TABLE receipts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    receipt_number VARCHAR(50) NOT NULL UNIQUE,
    customer_name VARCHAR(255) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'transfer', 'check') DEFAULT 'cash',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_customer (customer_name),
    INDEX idx_receipt_number (receipt_number),
    INDEX idx_created (created_at)
);

-- جدول المدفوعات للموردين
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    payment_number VARCHAR(50) NOT NULL UNIQUE,
    supplier_name VARCHAR(255) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'transfer', 'check') DEFAULT 'cash',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_supplier (supplier_name),
    INDEX idx_payment_number (payment_number),
    INDEX idx_created (created_at)
);

-- جدول الطابعات
CREATE TABLE printers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_active (is_active)
);

-- جدول سجل الأنشطة
CREATE TABLE activity_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    action VARCHAR(100) NOT NULL,
    details JSON,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_action (action),
    INDEX idx_created (created_at)
);

-- جدول الإعدادات
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key)
);

-- إدراج بيانات أولية للطابعات
INSERT INTO printers (name, description) VALUES
('الطابع الرئيسي', 'طابع الأعمال الأساسية'),
('طابع الألوان', 'طابع الأعمال الملونة'),
('طابع الكبير', 'طابع الأعمال الكبيرة');

-- إدراج إعدادات أولية
INSERT INTO settings (setting_key, setting_value, description) VALUES
('company_name', 'مطبعة الحديثة', 'اسم الشركة'),
('company_phone', '0123456789', 'رقم هاتف الشركة'),
('company_address', 'الرياض، المملكة العربية السعودية', 'عنوان الشركة'),
('receipt_prefix', 'REC', 'بادئة رقم الإيصال'),
('payment_prefix', 'PAY', 'بادئة رقم المدفوعة'),
('auto_backup', '1', 'النسخ الاحتياطي التلقائي'),
('backup_interval', '24', 'فترة النسخ الاحتياطي بالساعات');

-- إنشاء مستخدم قاعدة البيانات (اختياري)
-- CREATE USER 'printing_user'@'localhost' IDENTIFIED BY 'secure_password_2024';
-- GRANT ALL PRIVILEGES ON printing_system.* TO 'printing_user'@'localhost';
-- FLUSH PRIVILEGES;