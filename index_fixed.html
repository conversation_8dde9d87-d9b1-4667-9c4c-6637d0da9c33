<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🖨️ نظام إدارة الطباعة</title>
    
    <!-- مكتبة تحويل HTML إلى PDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            min-height: 100vh;
            color: #f1f5f9;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(59, 130, 246, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 5px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            color: #f1f5f9;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            font-weight: bold;
        }

        .tab.active {
            background: linear-gradient(135deg, #10b981, #059669);
            box-shadow: 0 5px 20px rgba(16, 185, 129, 0.4);
        }

        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            color: #1e293b;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }

        .tab-content.active {
            display: block;
        }

        .btn {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #374151;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .job-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #3b82f6;
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .job-title {
            font-size: 18px;
            font-weight: bold;
            color: #1e293b;
        }

        .job-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-in-progress {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .status-completed {
            background: #dcfce7;
            color: #16a34a;
        }

        .job-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .job-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .message.success {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            color: #166534;
            border: 1px solid #16a34a;
        }

        .message.error {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .message.info {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1e40af;
            border: 1px solid #3b82f6;
        }

        .message.warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #f59e0b;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            direction: rtl;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🖨️ نظام إدارة الطباعة</h1>
            <p>نظام شامل لإدارة أعمال الطباعة والعملاء</p>
        </div>

        <!-- Navigation Tabs -->
        <div class="tabs">
            <button class="tab active" onclick="showTab('jobs')">📋 الأعمال</button>
            <button class="tab" onclick="showTab('add-job')">➕ إضافة عمل</button>
            <button class="tab" onclick="showTab('customers')">👥 العملاء</button>
            <button class="tab" onclick="showTab('settings')">⚙️ الإعدادات</button>
        </div>

        <!-- Jobs Tab -->
        <div id="jobs" class="tab-content active">
            <h2>📋 قائمة الأعمال</h2>
            <div id="jobs-list">
                <!-- سيتم ملء الأعمال هنا -->
            </div>
        </div>

        <!-- Add Job Tab -->
        <div id="add-job" class="tab-content">
            <h2>➕ إضافة عمل جديد</h2>
            <form id="add-job-form">
                <div class="form-group">
                    <label for="job-name">نوع العمل:</label>
                    <input type="text" id="job-name" placeholder="مثال: طباعة بروشورات" required>
                </div>
                
                <div class="form-group">
                    <label for="job-quantity">الكمية:</label>
                    <input type="number" id="job-quantity" placeholder="100" required>
                </div>
                
                <div class="form-group">
                    <label for="job-price">السعر للوحدة:</label>
                    <input type="number" id="job-price" step="0.01" placeholder="2.50">
                </div>
                
                <div class="form-group">
                    <label for="job-customer">اسم العميل:</label>
                    <input type="text" id="job-customer" placeholder="اسم العميل">
                </div>
                
                <div class="form-group">
                    <label for="job-details">تفاصيل إضافية:</label>
                    <textarea id="job-details" rows="3" placeholder="تفاصيل العمل..."></textarea>
                </div>
                
                <button type="submit" class="btn btn-success">💾 حفظ العمل</button>
            </form>
        </div>

        <!-- Customers Tab -->
        <div id="customers" class="tab-content">
            <h2>👥 إدارة العملاء</h2>
            <div id="customers-list">
                <!-- سيتم ملء العملاء هنا -->
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <h2>⚙️ إعدادات النظام</h2>
            
            <div class="form-group">
                <label for="company-name">اسم الشركة:</label>
                <input type="text" id="company-name" value="مطبعة الحديثة">
            </div>
            
            <div class="form-group">
                <label for="company-phone">رقم الهاتف:</label>
                <input type="text" id="company-phone" value="*********">
            </div>
            
            <div style="background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border: 2px solid #0ea5e9; border-radius: 12px; padding: 20px; margin: 20px 0;">
                <h4 style="color: #0c4a6e; margin-bottom: 15px;">🚀 طريقة فتح الواتساب</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer; padding: 10px; background: white; border-radius: 8px; border: 2px solid #e2e8f0;">
                        <input type="radio" name="whatsappOpenMethod" value="smart" id="whatsappSmart" checked>
                        <div>
                            <strong>ذكي (موصى به)</strong><br>
                            <small style="color: #64748b;">يحاول فتح التطبيق أولاً، ثم الويب</small>
                        </div>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer; padding: 10px; background: white; border-radius: 8px; border: 2px solid #e2e8f0;">
                        <input type="radio" name="whatsappOpenMethod" value="app" id="whatsappApp">
                        <div>
                            <strong>التطبيق فقط</strong><br>
                            <small style="color: #64748b;">فتح التطبيق المحلي فقط</small>
                        </div>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer; padding: 10px; background: white; border-radius: 8px; border: 2px solid #e2e8f0;">
                        <input type="radio" name="whatsappOpenMethod" value="web" id="whatsappWeb">
                        <div>
                            <strong>الويب فقط</strong><br>
                            <small style="color: #64748b;">فتح WhatsApp Web فقط</small>
                        </div>
                    </label>
                </div>
                <div style="text-align: center; margin-top: 15px;">
                    <button type="button" onclick="testWhatsAppOpen()" class="btn" style="padding: 10px 20px; margin: 5px;">
                        🧪 اختبار فتح الواتساب
                    </button>
                </div>
            </div>
            
            <button onclick="saveSettings()" class="btn btn-success">💾 حفظ الإعدادات</button>
        </div>
    </div>

    <!-- Message Container -->
    <div id="message-container" style="position: fixed; top: 20px; right: 20px; z-index: 10001;"></div>

    <script>
        // متغيرات النظام
        let jobs = [];
        let customers = [];
        let systemSettings = {
            companyName: 'مطبعة الحديثة',
            companyPhone: '*********',
            companyEmail: '<EMAIL>',
            whatsappOpenMethod: 'smart'
        };

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            displayJobs();
            displayCustomers();
            loadSettings();
        });

        // عرض التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            const tabButtons = document.querySelectorAll('.tab');
            tabButtons.forEach(btn => btn.classList.remove('active'));
            
            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // إضافة عمل جديد
        document.getElementById('add-job-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const jobName = document.getElementById('job-name').value.trim();
            const jobQuantity = document.getElementById('job-quantity').value.trim();
            const jobPrice = document.getElementById('job-price').value.trim();
            const jobCustomer = document.getElementById('job-customer').value.trim();
            const jobDetails = document.getElementById('job-details').value.trim();
            
            if (!jobName || !jobQuantity) {
                showMessage('❌ يرجى إدخال نوع العمل والكمية', 'error');
                return;
            }
            
            const newJob = {
                id: Date.now(),
                name: jobName,
                totalQuantity: parseInt(jobQuantity),
                printedQuantity: 0,
                unitPrice: parseFloat(jobPrice) || 0,
                customerName: jobCustomer,
                details: jobDetails,
                status: 'pending',
                createdAt: new Date().toISOString(),
                invoiceNumber: jobCustomer ? `INV-${Date.now()}` : null
            };
            
            jobs.push(newJob);
            saveData();
            displayJobs();
            
            // مسح النموذج
            document.getElementById('add-job-form').reset();
            
            showMessage('✅ تم إضافة العمل بنجاح!', 'success');
            
            // إذا تم إدخال اسم العميل، اعرض خيارات المشاركة
            if (jobCustomer) {
                setTimeout(() => {
                    showShareOptionsAfterSave(newJob.id);
                }, 1000);
            }
            
            // الانتقال لتبويب الأعمال
            showTab('jobs');
        });

        // عرض الأعمال
        function displayJobs() {
            const jobsList = document.getElementById('jobs-list');

            if (jobs.length === 0) {
                jobsList.innerHTML = '<p style="text-align: center; color: #64748b;">لا توجد أعمال حالياً</p>';
                return;
            }

            jobsList.innerHTML = jobs.map(job => `
                <div class="job-card">
                    <div class="job-header">
                        <div class="job-title">${job.name}</div>
                        <div class="job-status status-${job.status}">
                            ${getStatusText(job.status)}
                        </div>
                    </div>

                    <div class="job-details">
                        <div><strong>الكمية:</strong> ${job.printedQuantity || 0} / ${job.totalQuantity}</div>
                        <div><strong>السعر:</strong> ${job.unitPrice ? job.unitPrice.toFixed(2) + ' ر.س' : 'غير محدد'}</div>
                        <div><strong>العميل:</strong> ${job.customerName || 'غير محدد'}</div>
                        <div><strong>رقم الفاتورة:</strong> ${job.invoiceNumber || 'غير محدد'}</div>
                    </div>

                    ${job.details ? `<div style="margin: 10px 0; padding: 10px; background: #f8fafc; border-radius: 8px;"><strong>التفاصيل:</strong> ${job.details}</div>` : ''}

                    <div class="job-actions">
                        <button class="btn" onclick="editJob(${job.id})">✏️ تعديل</button>
                        ${job.customerName && job.invoiceNumber ? `
                            <button class="btn btn-success" onclick="shareJobWhatsApp(${job.id})">📱 واتساب</button>
                            <button class="btn btn-danger" onclick="generateJobPDF(${job.id})">📄 PDF</button>
                        ` : ''}
                        <button class="btn btn-danger" onclick="deleteJob(${job.id})">🗑️ حذف</button>
                    </div>
                </div>
            `).join('');
        }

        // الحصول على نص الحالة
        function getStatusText(status) {
            const statusTexts = {
                'pending': 'في الانتظار',
                'in-progress': 'قيد الطباعة',
                'completed': 'مكتمل'
            };
            return statusTexts[status] || status;
        }

        // تعديل عمل
        function editJob(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            // ملء النموذج بالبيانات الحالية
            document.getElementById('job-name').value = job.name;
            document.getElementById('job-quantity').value = job.totalQuantity;
            document.getElementById('job-price').value = job.unitPrice || '';
            document.getElementById('job-customer').value = job.customerName || '';
            document.getElementById('job-details').value = job.details || '';

            // حذف العمل القديم
            jobs = jobs.filter(j => j.id !== jobId);
            saveData();
            displayJobs();

            // الانتقال لتبويب الإضافة
            showTab('add-job');
            showMessage('📝 تم تحميل بيانات العمل للتعديل', 'info');
        }

        // حذف عمل
        function deleteJob(jobId) {
            if (confirm('هل أنت متأكد من حذف هذا العمل؟')) {
                jobs = jobs.filter(j => j.id !== jobId);
                saveData();
                displayJobs();
                showMessage('✅ تم حذف العمل بنجاح', 'success');
            }
        }

        // إظهار خيارات المشاركة بعد حفظ العميل
        function showShareOptionsAfterSave(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job || !job.customerName) return;

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 20px;
                    padding: 30px;
                    max-width: 500px;
                    width: 90%;
                    text-align: center;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                    animation: modalSlideIn 0.3s ease-out;
                ">
                    <div style="
                        background: linear-gradient(135deg, #10b981, #059669);
                        color: white;
                        padding: 20px;
                        border-radius: 15px;
                        margin-bottom: 25px;
                    ">
                        <h2 style="margin: 0; font-size: 24px;">🎉 تم حفظ العميل بنجاح!</h2>
                        <p style="margin: 10px 0 0 0; opacity: 0.9;">
                            العميل: <strong>${job.customerName}</strong><br>
                            العمل: <strong>${job.name}</strong>
                        </p>
                    </div>

                    <div style="
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 15px;
                        margin-bottom: 20px;
                    ">
                        <button onclick="shareJobWhatsApp(${jobId}); closeModal()" style="
                            background: linear-gradient(135deg, #25d366, #128c7e);
                            color: white;
                            border: none;
                            padding: 15px 20px;
                            border-radius: 12px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            📱 إرسال واتساب
                        </button>

                        <button onclick="generateJobPDF(${jobId}); closeModal()" style="
                            background: linear-gradient(135deg, #ef4444, #dc2626);
                            color: white;
                            border: none;
                            padding: 15px 20px;
                            border-radius: 12px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            📄 تحويل PDF
                        </button>
                    </div>

                    <button onclick="closeModal()" style="
                        background: #6b7280;
                        color: white;
                        border: none;
                        padding: 10px 30px;
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 14px;
                    ">
                        ❌ إغلاق
                    </button>
                </div>
            `;

            window.closeModal = function() {
                if (modal && modal.parentNode) {
                    modal.remove();
                }
            };

            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });

            document.body.appendChild(modal);
        }

        // مشاركة العمل عبر WhatsApp
        function shareJobWhatsApp(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) {
                showMessage('❌ لم يتم العثور على العمل', 'error');
                return;
            }

            const message = generateWhatsAppMessage(job);
            openWhatsApp(message);
            showMessage('✅ تم فتح WhatsApp لإرسال الفاتورة', 'success');
        }

        // إنشاء رسالة WhatsApp
        function generateWhatsAppMessage(job) {
            const totalAmount = (job.printedQuantity || job.totalQuantity) * (job.unitPrice || 0);

            return `🖨️ *فاتورة طباعة*

👤 *العميل:* ${job.customerName}
📝 *نوع العمل:* ${job.name}
📊 *الكمية:* ${job.printedQuantity || job.totalQuantity}
💰 *السعر للوحدة:* ${job.unitPrice ? job.unitPrice.toFixed(2) + ' ر.س' : 'غير محدد'}
💵 *المبلغ الإجمالي:* ${totalAmount.toFixed(2)} ر.س
🔢 *رقم الفاتورة:* ${job.invoiceNumber}

${job.details ? `📋 *التفاصيل:* ${job.details}\n\n` : ''}🏢 *${systemSettings.companyName}*
📞 ${systemSettings.companyPhone}

🙏 شكراً لثقتكم بنا`;
        }

        // دالة ذكية لفتح WhatsApp
        function openWhatsApp(message, phoneNumber = '') {
            const encodedMessage = encodeURIComponent(message);
            const openMethod = systemSettings.whatsappOpenMethod || 'smart';

            console.log('📱 فتح WhatsApp بطريقة:', openMethod);

            const whatsappAppUrl = `whatsapp://send?text=${encodedMessage}${phoneNumber ? `&phone=${phoneNumber}` : ''}`;
            const whatsappWebUrl = phoneNumber ? `https://wa.me/${phoneNumber}?text=${encodedMessage}` : `https://wa.me/?text=${encodedMessage}`;

            switch (openMethod) {
                case 'app':
                    try {
                        window.location.href = whatsappAppUrl;
                        showMessage('✅ تم إرسال الأمر لفتح تطبيق WhatsApp', 'success');
                    } catch (error) {
                        showMessage('❌ لا يمكن فتح تطبيق WhatsApp. جرب طريقة أخرى.', 'error');
                    }
                    break;

                case 'web':
                    window.open(whatsappWebUrl, '_blank');
                    showMessage('✅ تم فتح WhatsApp Web', 'success');
                    break;

                case 'smart':
                default:
                    try {
                        window.location.href = whatsappAppUrl;
                        setTimeout(() => {
                            window.open(whatsappWebUrl, '_blank');
                        }, 2500);
                        showMessage('✅ تم محاولة فتح التطبيق، وسيتم فتح الويب كبديل', 'success');
                    } catch (error) {
                        window.open(whatsappWebUrl, '_blank');
                        showMessage('✅ تم فتح WhatsApp Web', 'success');
                    }
                    break;
            }
        }

        // تحويل العمل إلى PDF
        function generateJobPDF(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) {
                showMessage('❌ لم يتم العثور على العمل', 'error');
                return;
            }

            // إنشاء اسم الملف
            let fileName = '';
            if (job.customerName && job.invoiceNumber) {
                const cleanCustomerName = job.customerName
                    .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z0-9\s]/g, '')
                    .replace(/\s+/g, '_')
                    .substring(0, 30);
                fileName = `فاتورة_${cleanCustomerName}_${job.invoiceNumber}`;
            } else {
                fileName = `فاتورة_${job.id}`;
            }

            showMessage('🔄 جاري إنشاء ملف PDF...', 'info');

            // إنشاء محتوى الفاتورة
            const invoiceContent = generateInvoiceHTML(job);

            // إنشاء عنصر مؤقت
            const element = document.createElement('div');
            element.innerHTML = invoiceContent;
            element.style.direction = 'rtl';
            element.style.fontFamily = 'Arial, sans-serif';

            // تحويل إلى PDF
            if (typeof html2pdf !== 'undefined') {
                const opt = {
                    margin: 0.5,
                    filename: `${fileName}.pdf`,
                    image: { type: 'jpeg', quality: 0.98 },
                    html2canvas: {
                        scale: 2,
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: '#ffffff'
                    },
                    jsPDF: {
                        unit: 'in',
                        format: 'a4',
                        orientation: 'portrait',
                        compress: true
                    }
                };

                html2pdf().set(opt).from(element).save().then(() => {
                    showMessage(`✅ تم تحميل الملف: ${fileName}.pdf`, 'success');
                }).catch((error) => {
                    console.error('خطأ في إنشاء PDF:', error);
                    showMessage('❌ حدث خطأ في إنشاء ملف PDF', 'error');
                });
            } else {
                // طريقة بديلة
                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <!DOCTYPE html>
                    <html dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>${fileName}</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                            @media print { body { margin: 0; } }
                        </style>
                    </head>
                    <body>
                        ${invoiceContent}
                        <script>
                            window.onload = function() {
                                window.print();
                                setTimeout(() => window.close(), 1000);
                            };
                        </script>
                    </body>
                    </html>
                `);
                printWindow.document.close();
                showMessage('✅ تم فتح نافذة الطباعة', 'success');
            }
        }

        // إنشاء محتوى HTML للفاتورة
        function generateInvoiceHTML(job) {
            const invoiceDate = new Date().toLocaleDateString('ar-SA');
            const totalAmount = (job.printedQuantity || job.totalQuantity) * (job.unitPrice || 0);

            return `
                <div style="max-width: 800px; margin: 0 auto; font-family: Arial, sans-serif; direction: rtl; background: white; border: 2px solid #e2e8f0; border-radius: 15px; overflow: hidden;">
                    <div style="text-align: center; padding: 30px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white;">
                        <div style="font-size: 48px; margin-bottom: 10px;">🖨️</div>
                        <h1 style="margin: 0; font-size: 32px; font-weight: bold;">${systemSettings.companyName}</h1>
                        <p style="margin: 5px 0 0 0; font-size: 16px; opacity: 0.9;">للطباعة والتصميم</p>
                    </div>

                    <div style="padding: 30px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e2e8f0;">
                            <div>
                                <h2 style="margin: 0; color: #1e293b; font-size: 28px;">فاتورة طباعة</h2>
                                <p style="margin: 5px 0 0 0; color: #64748b;">رقم الفاتورة: <strong>${job.invoiceNumber || 'غير محدد'}</strong></p>
                            </div>
                            <div style="text-align: left;">
                                <p style="margin: 0; color: #64748b;">التاريخ: <strong>${invoiceDate}</strong></p>
                                <p style="margin: 5px 0 0 0; color: #64748b;">الوقت: <strong>${new Date().toLocaleTimeString('ar-SA')}</strong></p>
                            </div>
                        </div>

                        <div style="background: #f8fafc; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
                            <h3 style="margin: 0 0 15px 0; color: #1e293b;">بيانات العميل</h3>
                            <p style="margin: 0; font-size: 18px;"><strong>الاسم:</strong> ${job.customerName || 'غير محدد'}</p>
                        </div>

                        <div style="background: #f0f9ff; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
                            <h3 style="margin: 0 0 15px 0; color: #1e293b;">تفاصيل العمل</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <p style="margin: 0;"><strong>نوع العمل:</strong> ${job.name}</p>
                                <p style="margin: 0;"><strong>الكمية:</strong> ${job.printedQuantity || job.totalQuantity}</p>
                                <p style="margin: 0;"><strong>السعر للوحدة:</strong> ${(job.unitPrice || 0).toFixed(2)} ر.س</p>
                                <p style="margin: 0;"><strong>الحالة:</strong> ${getStatusText(job.status)}</p>
                            </div>
                            ${job.details ? `<p style="margin: 15px 0 0 0;"><strong>التفاصيل:</strong> ${job.details}</p>` : ''}
                        </div>

                        <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                            <h3 style="margin: 0; font-size: 24px;">المبلغ الإجمالي</h3>
                            <p style="margin: 10px 0 0 0; font-size: 32px; font-weight: bold;">${totalAmount.toFixed(2)} ر.س</p>
                        </div>

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0; color: #64748b;">
                            <p style="margin: 0;">🙏 شكراً لثقتكم بنا</p>
                            <p style="margin: 5px 0 0 0; font-size: 14px;">
                                📞 ${systemSettings.companyPhone} |
                                📧 ${systemSettings.companyEmail || '<EMAIL>'}
                            </p>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض العملاء
        function displayCustomers() {
            const customersList = document.getElementById('customers-list');

            // استخراج العملاء من الأعمال
            const uniqueCustomers = [...new Set(jobs.filter(job => job.customerName).map(job => job.customerName))];

            if (uniqueCustomers.length === 0) {
                customersList.innerHTML = '<p style="text-align: center; color: #64748b;">لا يوجد عملاء حالياً</p>';
                return;
            }

            customersList.innerHTML = uniqueCustomers.map(customerName => {
                const customerJobs = jobs.filter(job => job.customerName === customerName);
                const totalAmount = customerJobs.reduce((sum, job) => {
                    return sum + ((job.printedQuantity || job.totalQuantity) * (job.unitPrice || 0));
                }, 0);

                return `
                    <div class="job-card">
                        <div class="job-header">
                            <div class="job-title">👤 ${customerName}</div>
                            <div style="color: #10b981; font-weight: bold;">${totalAmount.toFixed(2)} ر.س</div>
                        </div>

                        <div class="job-details">
                            <div><strong>عدد الأعمال:</strong> ${customerJobs.length}</div>
                            <div><strong>آخر عمل:</strong> ${new Date(Math.max(...customerJobs.map(j => new Date(j.createdAt)))).toLocaleDateString('ar-SA')}</div>
                        </div>

                        <div class="job-actions">
                            <button class="btn" onclick="showCustomerJobs('${customerName}')">📋 عرض الأعمال</button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // عرض أعمال عميل محدد
        function showCustomerJobs(customerName) {
            const customerJobs = jobs.filter(job => job.customerName === customerName);

            if (customerJobs.length === 0) {
                showMessage('❌ لا توجد أعمال لهذا العميل', 'error');
                return;
            }

            // الانتقال لتبويب الأعمال وفلترة النتائج
            showTab('jobs');

            // فلترة الأعمال لإظهار أعمال العميل فقط
            const jobsList = document.getElementById('jobs-list');
            jobsList.innerHTML = customerJobs.map(job => `
                <div class="job-card" style="border-left-color: #10b981;">
                    <div class="job-header">
                        <div class="job-title">${job.name}</div>
                        <div class="job-status status-${job.status}">
                            ${getStatusText(job.status)}
                        </div>
                    </div>

                    <div class="job-details">
                        <div><strong>الكمية:</strong> ${job.printedQuantity || 0} / ${job.totalQuantity}</div>
                        <div><strong>السعر:</strong> ${job.unitPrice ? job.unitPrice.toFixed(2) + ' ر.س' : 'غير محدد'}</div>
                        <div><strong>رقم الفاتورة:</strong> ${job.invoiceNumber || 'غير محدد'}</div>
                        <div><strong>التاريخ:</strong> ${new Date(job.createdAt).toLocaleDateString('ar-SA')}</div>
                    </div>

                    ${job.details ? `<div style="margin: 10px 0; padding: 10px; background: #f8fafc; border-radius: 8px;"><strong>التفاصيل:</strong> ${job.details}</div>` : ''}

                    <div class="job-actions">
                        <button class="btn" onclick="editJob(${job.id})">✏️ تعديل</button>
                        ${job.invoiceNumber ? `
                            <button class="btn btn-success" onclick="shareJobWhatsApp(${job.id})">📱 واتساب</button>
                            <button class="btn btn-danger" onclick="generateJobPDF(${job.id})">📄 PDF</button>
                        ` : ''}
                        <button class="btn btn-danger" onclick="deleteJob(${job.id})">🗑️ حذف</button>
                    </div>
                </div>
            `).join('');

            // إضافة زر للعودة لجميع الأعمال
            jobsList.insertAdjacentHTML('afterbegin', `
                <div style="text-align: center; margin-bottom: 20px;">
                    <button class="btn" onclick="displayJobs()" style="background: #6b7280;">🔙 عرض جميع الأعمال</button>
                </div>
                <h3 style="color: #1e293b; margin-bottom: 20px;">أعمال العميل: ${customerName}</h3>
            `);
        }

        // حفظ الإعدادات
        function saveSettings() {
            systemSettings.companyName = document.getElementById('company-name').value;
            systemSettings.companyPhone = document.getElementById('company-phone').value;

            const whatsappMethod = document.querySelector('input[name="whatsappOpenMethod"]:checked');
            systemSettings.whatsappOpenMethod = whatsappMethod ? whatsappMethod.value : 'smart';

            localStorage.setItem('modernPrintingSettings', JSON.stringify(systemSettings));
            showMessage('✅ تم حفظ الإعدادات بنجاح', 'success');
        }

        // تحميل الإعدادات
        function loadSettings() {
            try {
                const savedSettings = localStorage.getItem('modernPrintingSettings');
                if (savedSettings) {
                    const parsedSettings = JSON.parse(savedSettings);
                    systemSettings = { ...systemSettings, ...parsedSettings };
                }

                // تطبيق الإعدادات على النموذج
                document.getElementById('company-name').value = systemSettings.companyName;
                document.getElementById('company-phone').value = systemSettings.companyPhone;

                const whatsappMethod = systemSettings.whatsappOpenMethod || 'smart';
                const methodRadio = document.getElementById('whatsapp' + whatsappMethod.charAt(0).toUpperCase() + whatsappMethod.slice(1));
                if (methodRadio) {
                    methodRadio.checked = true;
                }

            } catch (error) {
                console.error('خطأ في تحميل الإعدادات:', error);
            }
        }

        // اختبار فتح WhatsApp
        function testWhatsAppOpen() {
            const whatsappMethod = document.querySelector('input[name="whatsappOpenMethod"]:checked');
            systemSettings.whatsappOpenMethod = whatsappMethod ? whatsappMethod.value : 'smart';

            localStorage.setItem('modernPrintingSettings', JSON.stringify(systemSettings));

            const testMessage = `🧪 رسالة اختبار من نظام إدارة الطباعة

هذه رسالة اختبار للتأكد من عمل الواتساب بشكل صحيح.

📱 طريقة الفتح المختارة: ${systemSettings.whatsappOpenMethod}
⏰ وقت الاختبار: ${new Date().toLocaleString('ar-SA')}

🖨️ ${systemSettings.companyName}

✅ إذا وصلتك هذه الرسالة، فالإعدادات تعمل بشكل صحيح!`;

            openWhatsApp(testMessage);
        }

        // حفظ البيانات
        function saveData() {
            localStorage.setItem('modernPrintingJobs', JSON.stringify(jobs));
            localStorage.setItem('modernPrintingCustomers', JSON.stringify(customers));
        }

        // تحميل البيانات
        function loadData() {
            try {
                const savedJobs = localStorage.getItem('modernPrintingJobs');
                if (savedJobs) {
                    jobs = JSON.parse(savedJobs);
                }

                const savedCustomers = localStorage.getItem('modernPrintingCustomers');
                if (savedCustomers) {
                    customers = JSON.parse(savedCustomers);
                }

                const savedSettings = localStorage.getItem('modernPrintingSettings');
                if (savedSettings) {
                    const parsedSettings = JSON.parse(savedSettings);
                    systemSettings = { ...systemSettings, ...parsedSettings };
                }

            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
            }
        }

        // عرض الرسائل
        function showMessage(message, type = 'info') {
            const messageContainer = document.getElementById('message-container');

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                margin-bottom: 10px;
                animation: slideInRight 0.3s ease-out;
                max-width: 400px;
            `;

            messageContainer.appendChild(messageDiv);

            // إزالة الرسالة بعد 5 ثوانٍ
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => {
                        if (messageDiv.parentNode) {
                            messageDiv.remove();
                        }
                    }, 300);
                }
            }, 5000);
        }

        // إضافة CSS للحركات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(100%);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            @keyframes slideOutRight {
                from {
                    opacity: 1;
                    transform: translateX(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(100%);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
