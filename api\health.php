<?php
require_once '../config.php';

// فحص صحة النظام والاتصال بقاعدة البيانات
try {
    // فحص الاتصال بقاعدة البيانات
    $pdo = getDBConnection();
    $dbStatus = $pdo ? 'connected' : 'disconnected';
    
    // فحص الجداول المطلوبة
    $tables = ['jobs', 'customers', 'receipts', 'payments', 'printers', 'activity_log', 'settings'];
    $tablesStatus = [];
    
    if ($pdo) {
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT 1 FROM $table LIMIT 1");
                $tablesStatus[$table] = 'ok';
            } catch (PDOException $e) {
                $tablesStatus[$table] = 'error';
            }
        }
    }
    
    // إحصائيات سريعة
    $stats = [];
    if ($pdo) {
        try {
            // عدد الأعمال
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM jobs");
            $stats['total_jobs'] = $stmt->fetch()['count'];
            
            // عدد العملاء
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM customers");
            $stats['total_customers'] = $stmt->fetch()['count'];
            
            // عدد الإيصالات اليوم
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM receipts WHERE DATE(created_at) = CURDATE()");
            $stats['today_receipts'] = $stmt->fetch()['count'];
            
        } catch (PDOException $e) {
            $stats['error'] = 'فشل في جلب الإحصائيات';
        }
    }
    
    // معلومات الخادم
    $serverInfo = [
        'php_version' => PHP_VERSION,
        'server_time' => date('Y-m-d H:i:s'),
        'timezone' => date_default_timezone_get(),
        'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
        'max_execution_time' => ini_get('max_execution_time') . 's'
    ];
    
    sendResponse([
        'status' => 'healthy',
        'message' => 'النظام يعمل بشكل طبيعي',
        'database' => $dbStatus,
        'tables' => $tablesStatus,
        'statistics' => $stats,
        'server' => $serverInfo,
        'system' => [
            'name' => SYSTEM_NAME,
            'version' => SYSTEM_VERSION,
            'api_version' => API_VERSION
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log("Health check error: " . $e->getMessage());
    
    sendResponse([
        'status' => 'unhealthy',
        'message' => 'يوجد مشكلة في النظام',
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], 500);
}
?>