<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة الطباعة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .install-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2d3748;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #718096;
            font-size: 16px;
        }
        
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        
        .step.active {
            border-color: #4299e1;
            background: #ebf8ff;
        }
        
        .step.success {
            border-color: #48bb78;
            background: #f0fff4;
        }
        
        .step.error {
            border-color: #f56565;
            background: #fed7d7;
        }
        
        .step-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .step-content {
            color: #4a5568;
            line-height: 1.6;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4299e1;
        }
        
        .btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(66, 153, 225, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #3182ce);
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .icon {
            font-size: 20px;
        }
        
        .success { color: #48bb78; }
        .error { color: #f56565; }
        .warning { color: #ed8936; }
        .info { color: #4299e1; }
        
        .log {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="header">
            <h1>🖨️ تثبيت نظام إدارة الطباعة</h1>
            <p>إعداد قاعدة البيانات والنظام للعمل على الشبكة المحلية</p>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <form id="installForm">
            <div class="step active" id="step1">
                <div class="step-title">
                    <span class="icon">🔧</span>
                    إعدادات قاعدة البيانات
                </div>
                <div class="step-content">
                    <div class="form-group">
                        <label>خادم قاعدة البيانات:</label>
                        <input type="text" id="db_host" value="localhost" required>
                    </div>
                    <div class="form-group">
                        <label>اسم قاعدة البيانات:</label>
                        <input type="text" id="db_name" value="printing_system" required>
                    </div>
                    <div class="form-group">
                        <label>اسم المستخدم:</label>
                        <input type="text" id="db_user" value="root" required>
                    </div>
                    <div class="form-group">
                        <label>كلمة المرور:</label>
                        <input type="password" id="db_pass" placeholder="اتركه فارغاً إذا لم تكن هناك كلمة مرور">
                    </div>
                </div>
            </div>

            <div class="step" id="step2">
                <div class="step-title">
                    <span class="icon">⚙️</span>
                    إعدادات النظام
                </div>
                <div class="step-content">
                    <div class="form-group">
                        <label>اسم الشركة/المطبعة:</label>
                        <input type="text" id="company_name" value="مطبعة الحديثة" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف:</label>
                        <input type="text" id="company_phone" value="0123456789">
                    </div>
                    <div class="form-group">
                        <label>العنوان:</label>
                        <input type="text" id="company_address" value="الرياض، المملكة العربية السعودية">
                    </div>
                </div>
            </div>

            <div class="step" id="step3">
                <div class="step-title">
                    <span class="icon">📊</span>
                    حالة التثبيت
                </div>
                <div class="step-content">
                    <div id="installStatus">جاري التحضير للتثبيت...</div>
                    <div class="log" id="installLog" style="display: none;"></div>
                </div>
            </div>

            <button type="submit" class="btn" id="installBtn">
                🚀 بدء التثبيت
            </button>
        </form>
    </div>

    <script>
        let currentStep = 1;
        const totalSteps = 3;

        document.getElementById('installForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            await startInstallation();
        });

        async function startInstallation() {
            const btn = document.getElementById('installBtn');
            const log = document.getElementById('installLog');
            const status = document.getElementById('installStatus');
            
            btn.disabled = true;
            btn.textContent = '⏳ جاري التثبيت...';
            log.style.display = 'block';
            
            try {
                // الخطوة 1: فحص الاتصال
                updateProgress(33);
                updateStep(2);
                addLog('🔍 فحص الاتصال بقاعدة البيانات...');
                
                const dbConfig = {
                    host: document.getElementById('db_host').value,
                    name: document.getElementById('db_name').value,
                    user: document.getElementById('db_user').value,
                    pass: document.getElementById('db_pass').value
                };
                
                const testResult = await testDatabaseConnection(dbConfig);
                if (!testResult.success) {
                    throw new Error(testResult.error);
                }
                addLog('✅ تم الاتصال بقاعدة البيانات بنجاح');
                
                // الخطوة 2: إنشاء الجداول
                updateProgress(66);
                addLog('🏗️ إنشاء جداول قاعدة البيانات...');
                
                const createResult = await createDatabaseTables(dbConfig);
                if (!createResult.success) {
                    throw new Error(createResult.error);
                }
                addLog('✅ تم إنشاء جميع الجداول بنجاح');
                
                // الخطوة 3: إدراج البيانات الأولية
                updateProgress(100);
                updateStep(3);
                addLog('📝 إدراج البيانات الأولية...');
                
                const companyData = {
                    name: document.getElementById('company_name').value,
                    phone: document.getElementById('company_phone').value,
                    address: document.getElementById('company_address').value
                };
                
                const dataResult = await insertInitialData(dbConfig, companyData);
                if (!dataResult.success) {
                    throw new Error(dataResult.error);
                }
                addLog('✅ تم إدراج البيانات الأولية بنجاح');
                
                // إنهاء التثبيت
                document.getElementById('step3').classList.add('success');
                status.innerHTML = `
                    <div style="text-align: center; color: #48bb78; font-size: 18px; font-weight: bold;">
                        🎉 تم التثبيت بنجاح!
                    </div>
                    <div style="margin-top: 15px; text-align: center;">
                        <a href="index.html" style="background: #48bb78; color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none; display: inline-block;">
                            🚀 بدء استخدام النظام
                        </a>
                    </div>
                `;
                
                addLog('🎊 التثبيت مكتمل! يمكنك الآن استخدام النظام');
                
            } catch (error) {
                document.getElementById('step' + currentStep).classList.add('error');
                addLog('❌ خطأ: ' + error.message, 'error');
                status.innerHTML = `<div style="color: #f56565;">فشل التثبيت: ${error.message}</div>`;
                
                btn.disabled = false;
                btn.textContent = '🔄 إعادة المحاولة';
            }
        }

        function updateProgress(percent) {
            document.getElementById('progressBar').style.width = percent + '%';
        }

        function updateStep(step) {
            if (currentStep < totalSteps) {
                document.getElementById('step' + currentStep).classList.remove('active');
                document.getElementById('step' + currentStep).classList.add('success');
            }
            
            if (step <= totalSteps) {
                currentStep = step;
                document.getElementById('step' + step).classList.add('active');
            }
        }

        function addLog(message, type = 'info') {
            const log = document.getElementById('installLog');
            const time = new Date().toLocaleTimeString('ar-SA');
            const color = type === 'error' ? '#f56565' : type === 'success' ? '#48bb78' : '#4299e1';
            
            log.innerHTML += `<div style="color: ${color};">[${time}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }

        // محاكاة وظائف API (في التطبيق الحقيقي ستكون طلبات HTTP)
        async function testDatabaseConnection(config) {
            await sleep(1000);
            return { success: true };
        }

        async function createDatabaseTables(config) {
            await sleep(2000);
            return { success: true };
        }

        async function insertInitialData(config, companyData) {
            await sleep(1000);
            return { success: true };
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    </script>
</body>
</html>

<?php
// معالجة طلبات التثبيت عبر POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'test_connection':
                echo json_encode(testConnection($input['config']));
                break;
                
            case 'create_tables':
                echo json_encode(createTables($input['config']));
                break;
                
            case 'insert_data':
                echo json_encode(insertData($input['config'], $input['company']));
                break;
                
            default:
                echo json_encode(['success' => false, 'error' => 'إجراء غير معروف']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

function testConnection($config) {
    try {
        $dsn = "mysql:host={$config['host']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass']);
        return ['success' => true];
    } catch (PDOException $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function createTables($config) {
    try {
        $dsn = "mysql:host={$config['host']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass']);
        
        // قراءة وتنفيذ ملف SQL
        $sql = file_get_contents(__DIR__ . '/database.sql');
        $pdo->exec($sql);
        
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function insertData($config, $company) {
    try {
        $dsn = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass']);
        
        // تحديث بيانات الشركة
        $stmt = $pdo->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = ?");
        $stmt->execute([$company['name'], 'company_name']);
        $stmt->execute([$company['phone'], 'company_phone']);
        $stmt->execute([$company['address'], 'company_address']);
        
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}
?>