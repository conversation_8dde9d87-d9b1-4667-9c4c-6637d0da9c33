<?php
require_once '../config.php';

// التحقق من API Key
validateApiKey();

// الحصول على طريقة الطلب
$method = $_SERVER['REQUEST_METHOD'];

// معالجة طلبات OPTIONS للـ CORS
if ($method === 'OPTIONS') {
    sendResponse(['message' => 'OK']);
}

try {
    $pdo = getDBConnection();
    if (!$pdo) {
        sendError('فشل الاتصال بقاعدة البيانات', 500);
    }

    switch ($method) {
        case 'GET':
            handleGetJobs($pdo);
            break;
        case 'POST':
            handleCreateJob($pdo);
            break;
        case 'PUT':
            handleUpdateJob($pdo);
            break;
        case 'DELETE':
            handleDeleteJob($pdo);
            break;
        default:
            sendError('طريقة الطلب غير مدعومة', 405);
    }
} catch (Exception $e) {
    error_log("Jobs API Error: " . $e->getMessage());
    sendError('خطأ في الخادم', 500);
}

// دالة جلب الأعمال
function handleGetJobs($pdo) {
    $id = $_GET['id'] ?? null;
    
    if ($id) {
        // جلب عمل واحد
        $stmt = $pdo->prepare("SELECT * FROM jobs WHERE id = ?");
        $stmt->execute([$id]);
        $job = $stmt->fetch();
        
        if ($job) {
            sendSuccess($job);
        } else {
            sendError('العمل غير موجود', 404);
        }
    } else {
        // جلب جميع الأعمال
        $limit = $_GET['limit'] ?? 100;
        $offset = $_GET['offset'] ?? 0;
        $status = $_GET['status'] ?? null;
        $customer = $_GET['customer'] ?? null;
        
        $sql = "SELECT * FROM jobs WHERE 1=1";
        $params = [];
        
        if ($status) {
            $sql .= " AND status = ?";
            $params[] = $status;
        }
        
        if ($customer) {
            $sql .= " AND customer_name LIKE ?";
            $params[] = "%$customer%";
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = (int)$limit;
        $params[] = (int)$offset;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $jobs = $stmt->fetchAll();
        
        // حساب العدد الإجمالي
        $countSql = "SELECT COUNT(*) as total FROM jobs WHERE 1=1";
        $countParams = [];
        
        if ($status) {
            $countSql .= " AND status = ?";
            $countParams[] = $status;
        }
        
        if ($customer) {
            $countSql .= " AND customer_name LIKE ?";
            $countParams[] = "%$customer%";
        }
        
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($countParams);
        $total = $countStmt->fetch()['total'];
        
        sendSuccess([
            'jobs' => $jobs,
            'total' => $total,
            'limit' => (int)$limit,
            'offset' => (int)$offset
        ]);
    }
}

// دالة إنشاء عمل جديد
function handleCreateJob($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendError('بيانات غير صحيحة');
    }
    
    $requiredFields = ['job_name'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            sendError("الحقل $field مطلوب");
        }
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO jobs (job_name, job_details, quantity, printer_name, invoice_number, customer_name, amount, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $input['job_name'],
            $input['job_details'] ?? '',
            $input['quantity'] ?? 0,
            $input['printer_name'] ?? '',
            $input['invoice_number'] ?? '',
            $input['customer_name'] ?? '',
            $input['amount'] ?? 0.00,
            $input['status'] ?? 'pending'
        ]);
        
        $jobId = $pdo->lastInsertId();
        
        // تحديث إحصائيات العميل
        if (!empty($input['customer_name'])) {
            updateCustomerStats($pdo, $input['customer_name']);
        }
        
        // تسجيل النشاط
        logActivity('create_job', ['job_id' => $jobId, 'job_name' => $input['job_name']]);
        
        sendSuccess(['id' => $jobId], 'تم إنشاء العمل بنجاح');
        
    } catch (PDOException $e) {
        error_log("Create job error: " . $e->getMessage());
        sendError('فشل في إنشاء العمل', 500);
    }
}

// دالة تحديث عمل
function handleUpdateJob($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    $id = $_GET['id'] ?? $input['id'] ?? null;
    
    if (!$id) {
        sendError('معرف العمل مطلوب');
    }
    
    if (!$input) {
        sendError('بيانات غير صحيحة');
    }
    
    // التحقق من وجود العمل
    $stmt = $pdo->prepare("SELECT * FROM jobs WHERE id = ?");
    $stmt->execute([$id]);
    $existingJob = $stmt->fetch();
    
    if (!$existingJob) {
        sendError('العمل غير موجود', 404);
    }
    
    try {
        $updateFields = [];
        $params = [];
        
        $allowedFields = ['job_name', 'job_details', 'quantity', 'printer_name', 'invoice_number', 'customer_name', 'amount', 'status'];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "$field = ?";
                $params[] = $input[$field];
            }
        }
        
        if (empty($updateFields)) {
            sendError('لا توجد حقول للتحديث');
        }
        
        $params[] = $id;
        
        $sql = "UPDATE jobs SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        // تحديث إحصائيات العميل القديم والجديد
        if (isset($input['customer_name']) && $input['customer_name'] !== $existingJob['customer_name']) {
            if ($existingJob['customer_name']) {
                updateCustomerStats($pdo, $existingJob['customer_name']);
            }
            if ($input['customer_name']) {
                updateCustomerStats($pdo, $input['customer_name']);
            }
        }
        
        // تسجيل النشاط
        logActivity('update_job', ['job_id' => $id, 'changes' => $input]);
        
        sendSuccess(null, 'تم تحديث العمل بنجاح');
        
    } catch (PDOException $e) {
        error_log("Update job error: " . $e->getMessage());
        sendError('فشل في تحديث العمل', 500);
    }
}

// دالة حذف عمل
function handleDeleteJob($pdo) {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        sendError('معرف العمل مطلوب');
    }
    
    // التحقق من وجود العمل
    $stmt = $pdo->prepare("SELECT * FROM jobs WHERE id = ?");
    $stmt->execute([$id]);
    $job = $stmt->fetch();
    
    if (!$job) {
        sendError('العمل غير موجود', 404);
    }
    
    try {
        $stmt = $pdo->prepare("DELETE FROM jobs WHERE id = ?");
        $stmt->execute([$id]);
        
        // تحديث إحصائيات العميل
        if ($job['customer_name']) {
            updateCustomerStats($pdo, $job['customer_name']);
        }
        
        // تسجيل النشاط
        logActivity('delete_job', ['job_id' => $id, 'job_name' => $job['job_name']]);
        
        sendSuccess(null, 'تم حذف العمل بنجاح');
        
    } catch (PDOException $e) {
        error_log("Delete job error: " . $e->getMessage());
        sendError('فشل في حذف العمل', 500);
    }
}

// دالة تحديث إحصائيات العميل
function updateCustomerStats($pdo, $customerName) {
    if (!$customerName) return;
    
    try {
        // حساب إحصائيات العميل
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_jobs,
                COALESCE(SUM(amount), 0) as total_amount
            FROM jobs 
            WHERE customer_name = ?
        ");
        $stmt->execute([$customerName]);
        $stats = $stmt->fetch();
        
        // تحديث أو إنشاء العميل
        $stmt = $pdo->prepare("
            INSERT INTO customers (name, type, total_jobs, total_amount) 
            VALUES (?, 'customer', ?, ?)
            ON DUPLICATE KEY UPDATE 
                total_jobs = VALUES(total_jobs),
                total_amount = VALUES(total_amount),
                updated_at = NOW()
        ");
        $stmt->execute([$customerName, $stats['total_jobs'], $stats['total_amount']]);
        
    } catch (PDOException $e) {
        error_log("Update customer stats error: " . $e->getMessage());
    }
}
?>