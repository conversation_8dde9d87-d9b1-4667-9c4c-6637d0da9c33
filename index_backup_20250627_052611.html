<!DOCTYPE html>
<!-- saved from url=(0017)http://localhost/ -->
<html lang="ar" dir="rtl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🖨️ نظام إدارة الطباعة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #**********%);
            min-height: 100vh;
            color: #f1f5f9;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #1e293b 0%, #**********%);
            border: 1px solid #475569;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header h1 {
            font-size: 28px;
            font-weight: bold;
            color: #f1f5f9;
            margin: 0;
        }

        .tabs {
            background: linear-gradient(135deg, #1e293b 0%, #**********%);
            border: 1px solid #475569;
            border-radius: 16px;
            padding: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .tab {
            flex: 1;
            padding: 12px 20px;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 16px;
            border: 1px solid transparent;
        }

        .tab:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
        }

        .tab.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
        }

        .content {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            color: #1e293b;
        }

        .search-container {
            margin-bottom: 24px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 16px 20px 16px 50px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            background: white;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            font-size: 18px;
        }

        .jobs-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }

        .jobs-grid .job-card {
            flex: 1 1 320px;
            max-width: 450px;
        }

        .job-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }

        .job-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        }

        .job-card.urgent {
            border: 3px solid #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            position: relative;
            animation: urgentPulse 2s infinite;
            box-shadow: 0 8px 32px rgba(239, 68, 68, 0.4);
            order: -1;
            transform: scale(1.02);
        }

        .job-card.urgent::before {
            content: '🚨';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #ef4444;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            animation: urgentBounce 1s infinite;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.5);
        }

        @keyframes urgentPulse {
            0%, 100% {
                box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);
            }
            50% {
                box-shadow: 0 8px 32px rgba(239, 68, 68, 0.6);
            }
        }

        @keyframes urgentBounce {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        .job-card.in-progress {
            border-left: 6px solid #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        }

        .job-card.completed {
            border-left: 6px solid #10b981;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            position: relative;
        }

        .job-card.completed::after {
            content: '🔒';
            position: absolute;
            top: 10px;
            left: 10px;
            background: #10b981;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .job-title {
            font-size: 24px;
            font-weight: bold;
            color: #1e293b;
            line-height: 1.3;
        }

        .job-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-urgent {
            background: #ef4444;
            color: white;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
        }

        .status-progress {
            background: #f59e0b;
            color: white;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
        }

        .status-completed {
            background: #10b981;
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
        }

        .status-pending {
            background: #f97316;
            color: white;
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.4);
        }

        .job-details {
            margin-bottom: 16px;
            line-height: 1.6;
        }

        .job-detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 4px 0;
        }

        .job-detail-label {
            font-weight: 600;
            color: #64748b;
        }

        .job-detail-value {
            color: #1e293b;
            font-weight: 500;
        }

        .job-actions {
            display: flex;
            gap: 8px;
            margin-top: 16px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .form-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 16px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #3b82f6;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 24px;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-card.active {
            border: 2px solid #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .stat-card.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        }

        /* مربع في الانتظار - لون برتقالي */
        #stat-pending {
            background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
            border: 2px solid #f97316;
            color: #9a3412;
        }

        #stat-pending .stat-number {
            color: #ea580c;
            font-weight: 800;
        }

        #stat-pending.active {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            border: 2px solid #ea580c;
            color: white;
            box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);
        }

        #stat-pending.active .stat-number {
            color: white;
        }

        #stat-pending.active::before {
            background: linear-gradient(90deg, #f97316, #ea580c);
        }

        /* مربع قيد الطباعة - لون أصفر */
        #stat-progress {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            color: #92400e;
        }

        #stat-progress .stat-number {
            color: #d97706;
            font-weight: 800;
        }

        #stat-progress.active {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            border: 2px solid #d97706;
            color: white;
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
        }

        #stat-progress.active .stat-number {
            color: white;
        }

        #stat-progress.active::before {
            background: linear-gradient(90deg, #f59e0b, #d97706);
        }

        /* مربع تم الطباعة - لون أخضر */
        #stat-printed {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 2px solid #10b981;
            color: #065f46;
        }

        #stat-printed .stat-number {
            color: #059669;
            font-weight: 800;
        }

        #stat-printed.active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border: 2px solid #059669;
            color: white;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        #stat-printed.active .stat-number {
            color: white;
        }

        #stat-printed.active::before {
            background: linear-gradient(90deg, #10b981, #059669);
        }

        /* مربع مرحلة - لون بنفسجي */
        #stat-completed {
            background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
            border: 2px solid #8b5cf6;
            color: #5b21b6;
        }

        #stat-completed .stat-number {
            color: #7c3aed;
            font-weight: 800;
        }

        #stat-completed.active {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            border: 2px solid #7c3aed;
            color: white;
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
        }

        #stat-completed.active .stat-number {
            color: white;
        }

        #stat-completed.active::before {
            background: linear-gradient(90deg, #8b5cf6, #7c3aed);
        }

        /* مربع إجمالي الأعمال - لون أزرق */
        #stat-all {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 2px solid #3b82f6;
            color: #1e40af;
        }

        #stat-all .stat-number {
            color: #2563eb;
            font-weight: 800;
        }

        #stat-all.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border: 2px solid #2563eb;
            color: white;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        #stat-all.active .stat-number {
            color: white;
        }

        #stat-all.active::before {
            background: linear-gradient(90deg, #3b82f6, #2563eb);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 8px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #64748b;
        }

        .empty-state-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .message {
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            font-weight: 600;
            text-align: center;
        }

        .message.success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid #10b981;
        }

        .message.error {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .hidden {
            display: none !important;
        }

        /* تنسيقات النظام المحاسبي */
        .accounting-content {
            display: block;
        }

        .accounting-content.hidden {
            display: none;
        }

        /* خلفية مميزة لأقسام النظام المحاسبي */
        .accounting-content .form-section {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%) !important;
            border: 2px solid #0ea5e9 !important;
            box-shadow: 0 8px 32px rgba(14, 165, 233, 0.2) !important;
        }

        .accounting-content .report-section {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%) !important;
            border: 2px solid #0ea5e9 !important;
            box-shadow: 0 8px 32px rgba(14, 165, 233, 0.2) !important;
        }

        /* خلفية عامة للنظام المحاسبي بالكامل */
        #accounting-section {
            background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 30%, #7dd3fc 70%, #38bdf8 100%);
            border-radius: 20px;
            padding: 25px;
            margin: 15px 0;
            box-shadow: 0 12px 40px rgba(14, 165, 233, 0.25);
            border: 3px solid #0ea5e9;
            position: relative;
        }

        /* تأثير إضافي للنظام المحاسبي */
        #accounting-section::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #0ea5e9, #38bdf8, #0ea5e9);
            border-radius: 22px;
            z-index: -1;
            animation: accountingGlow 3s ease-in-out infinite alternate;
        }

        @keyframes accountingGlow {
            0% {
                box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
            }
            100% {
                box-shadow: 0 0 40px rgba(14, 165, 233, 0.6);
            }
        }

        /* تغيير خلفية المحتوى عندما يكون النظام المحاسبي مفتوحاً */
        .content.accounting-active {
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #**********%) !important;
            color: #f1f5f9 !important;
        }

        /* تأكد من أن النصوص تظهر بوضوح في النظام المحاسبي */
        .content.accounting-active h2,
        .content.accounting-active h3,
        .content.accounting-active .stat-label {
            color: #f1f5f9 !important;
        }

        /* بطاقات الفواتير */
        .invoice-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .invoice-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .invoice-card.paid {
            border-left: 6px solid #10b981;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        }

        .invoice-card.pending {
            border-left: 6px solid #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        }

        .invoice-card.overdue {
            border-left: 6px solid #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .invoice-number {
            font-size: 20px;
            font-weight: bold;
            color: #1e293b;
        }

        .invoice-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
        }

        .status-paid {
            background: #10b981;
            color: white;
        }

        .status-pending {
            background: #f59e0b;
            color: white;
        }

        .status-overdue {
            background: #ef4444;
            color: white;
        }

        .invoice-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 16px;
        }

        .invoice-detail {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .invoice-detail-label {
            font-weight: 600;
            color: #64748b;
        }

        .invoice-detail-value {
            color: #1e293b;
            font-weight: 500;
        }

        .invoice-amount {
            font-size: 24px;
            font-weight: bold;
            color: #059669;
            text-align: center;
            padding: 16px;
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border-radius: 8px;
            margin: 16px 0;
        }

        /* بطاقات العملاء */
        .customer-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .customer-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .customer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .customer-name {
            font-size: 20px;
            font-weight: bold;
            color: #1e293b;
        }

        .customer-balance {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 700;
        }

        .balance-positive {
            background: #dcfce7;
            color: #166534;
        }

        .balance-negative {
            background: #fee2e2;
            color: #991b1b;
        }

        .balance-zero {
            background: #f1f5f9;
            color: #64748b;
        }

        /* بطاقات المدفوعات */
        .payment-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            border-left: 6px solid #10b981;
        }

        .payment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .payment-amount {
            font-size: 20px;
            font-weight: bold;
            color: #059669;
        }

        .payment-method {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            background: #eff6ff;
            color: #1d4ed8;
        }

        /* تقارير */
        .report-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .report-title {
            font-size: 24px;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }

        .report-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .summary-item {
            text-align: center;
            padding: 16px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 8px;
        }

        .summary-value {
            font-size: 28px;
            font-weight: bold;
            color: #1e293b;
        }

        .summary-label {
            font-size: 14px;
            color: #64748b;
            margin-top: 4px;
        }

        /* تنسيقات السندات */
        .receipt-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            border-left: 6px solid #10b981;
            transition: all 0.3s ease;
        }

        .receipt-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .payment-out-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            border-left: 6px solid #ef4444;
            transition: all 0.3s ease;
        }

        .payment-out-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .receipt-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .receipt-number {
            font-size: 18px;
            font-weight: bold;
            color: #1e293b;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            padding: 8px 16px;
            border-radius: 20px;
            border: 2px solid #0ea5e9;
        }

        .receipt-amount {
            font-size: 20px;
            font-weight: bold;
            color: #059669;
        }

        .payment-out-amount {
            font-size: 20px;
            font-weight: bold;
            color: #dc2626;
        }

        /* كشف الحساب المحسن */
        .statement-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border: 3px solid #0ea5e9;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
        }

        .statement-table th {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            padding: 18px 15px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
            border-right: 2px solid #0284c7;
            border-bottom: 3px solid #0284c7;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            position: relative;
        }

        .statement-table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #38bdf8, #0ea5e9, #38bdf8);
        }

        .statement-table th:last-child {
            border-right: none;
        }

        .statement-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
            border-right: 1px solid #e2e8f0;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.2s ease;
            color: #1e293b !important;
        }

        .statement-table td:last-child {
            border-right: none;
        }

        .statement-table tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        .statement-table tr:nth-child(odd) {
            background: white;
        }

        .statement-table tr:hover {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            transform: scale(1.01);
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.1);
        }

        /* تحسين خلايا المبالغ */
        .statement-table td.amount-positive {
            color: #059669;
            font-weight: bold;
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
        }

        .statement-table td.amount-negative {
            color: #dc2626;
            font-weight: bold;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        }

        /* تحسين الصف الأول والأخير */
        .statement-table tr:first-child td {
            border-top: 2px solid #0ea5e9;
        }

        .statement-table tr:last-child td {
            border-bottom: 3px solid #0ea5e9;
            font-weight: bold;
        }

        .statement-balance {
            font-weight: bold;
            padding: 25px;
            text-align: center;
            font-size: 28px;
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            color: #059669;
            border-radius: 16px;
            margin: 25px 0;
            border: 3px solid #10b981;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);
            position: relative;
            overflow: hidden;
        }

        .statement-balance::before {
            content: '💰';
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            opacity: 0.7;
        }

        .statement-balance.negative {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            color: #dc2626;
            border-color: #ef4444;
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.2);
        }

        .statement-balance.negative::before {
            content: '⚠️';
        }

        /* تحسين عنوان كشف الحساب */
        .statement-header {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            padding: 20px;
            border-radius: 16px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
            border: 2px solid #0284c7;
        }

        .statement-header h2 {
            margin: 0;
            font-size: 24px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .statement-header .date-info {
            margin-top: 10px;
            font-size: 16px;
            opacity: 0.9;
        }

        /* تأكد من وضوح النصوص في كشف الحساب */
        .accounting-content .report-title {
            color: #1e293b !important;
            font-weight: bold;
        }

        .accounting-content .statement-table td,
        .accounting-content .statement-table th {
            color: #1e293b !important;
        }

        .accounting-content .statement-table th {
            color: white !important;
        }

        /* تحسين معلومات التاريخ */
        .statement-date-info {
            background: #f8fafc !important;
            color: #64748b !important;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            margin-bottom: 20px;
            border: 1px solid #e2e8f0;
        }

        /* تحسين الصفوف القابلة للضغط في كشف الحساب */
        .invoice-row-clickable {
            transition: all 0.3s ease;
            position: relative;
        }

        .invoice-row-clickable:hover {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.2);
            border-left: 4px solid #0ea5e9;
        }

        .invoice-row-clickable:hover td {
            color: #0c4a6e !important;
            font-weight: 600;
        }

        .invoice-row-clickable::after {
            content: '✏️ اضغط للتعديل';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: #0ea5e9;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .invoice-row-clickable:hover::after {
            opacity: 1;
        }

        /* تحسينات إضافية */
        .form-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }

        .tabs {
            overflow-x: auto;
            white-space: nowrap;
            padding-bottom: 5px;
        }

        .tabs::-webkit-scrollbar {
            height: 4px;
        }

        .tabs::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .tabs::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }

        .tabs::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .customer-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            margin-top: 4px;
        }

        .customer-suggestion-header {
            padding: 12px 16px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-weight: bold;
            color: #475569;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e2e8f0;
        }

        .customer-suggestion-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .customer-suggestion-item:hover {
            background: #f0f9ff;
            transform: translateX(4px);
        }

        .customer-suggestion-item:last-child {
            border-bottom: none;
        }

        .customer-suggestion-item.registered {
            border-left: 4px solid #10b981;
        }

        .customer-suggestion-item.existing {
            border-left: 4px solid #f59e0b;
        }

        .customer-suggestion-item.add-new {
            border-left: 4px solid #6366f1;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
            color: #4338ca;
            font-weight: bold;
        }

        .customer-suggestion-item.add-new:hover {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
        }

        .customer-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .customer-name {
            font-weight: 600;
            color: #1e293b;
        }

        .customer-phone {
            font-size: 12px;
            color: #64748b;
        }

        .customer-badge {
            font-size: 11px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .customer-badge.registered {
            background: #dcfce7;
            color: #166534;
        }

        .customer-badge.existing {
            background: #fef3c7;
            color: #92400e;
        }

        .customer-suggestion-item.existing {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border-left: 4px solid #10b981;
        }

        /* مؤشر حالة الاتصال */
        .connection-status {
            position: fixed;
            top: 20px;
            left: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .connection-status.online {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .connection-status.offline {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
        }

        .connection-status.syncing {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* أزرار التعديل الصغيرة */
        .btn-edit-small {
            background: none;
            border: none;
            font-size: 14px;
            cursor: pointer;
            padding: 2px 5px;
            border-radius: 4px;
            transition: all 0.2s ease;
            margin-right: 5px;
        }

        .btn-edit-small:hover {
            background: #f3f4f6;
            transform: scale(1.1);
        }

        /* أزرار المشاركة */
        .share-actions {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid #cbd5e1;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 10px;
            margin-top: 15px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 12px;
            border: 2px dashed #cbd5e1;
        }

        .share-btn {
            flex: 1;
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .share-btn.print {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

        .share-btn.whatsapp {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .share-btn.pdf {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .share-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }

        /* أنماط نافذة خيارات المشاركة */
        .whatsapp-share-modal {
            max-width: 600px;
            width: 90%;
        }

        .share-options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .share-option-btn {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 24px 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            min-height: 160px;
        }

        .share-option-btn:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .share-option-btn.text-option:hover {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        }

        .share-option-btn.image-option:hover {
            border-color: #10b981;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        }

        .share-option-btn.pdf-option:hover {
            border-color: #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        }

        .option-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .option-title {
            font-size: 18px;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .option-desc {
            font-size: 14px;
            color: #64748b;
            text-align: center;
            line-height: 1.4;
        }

        .customer-suggestion-item.existing:hover {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
        }

        .customer-badge {
            background: #10b981;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .tabs {
                flex-direction: column;
            }

            .jobs-grid {
                grid-template-columns: 1fr;
            }

            .job-actions {
                flex-direction: column;
            }

            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- مؤشر حالة الاتصال -->
    <div id="connectionStatus" class="connection-status offline">📱 وضع محلي</div>
    <div class="container">
        <!-- العنوان الرئيسي -->
        <div class="header">
            <h1>🖨️ نظام إدارة الطباعة</h1>
        </div>

        <!-- التبويبات -->
        <div class="tabs">
            <div class="tab active" onclick="switchTab(&#39;jobs&#39;)">الأعمال</div>
            <div class="tab" onclick="switchTab(&#39;add-job&#39;)">إضافة عمل</div>
            <div class="tab" onclick="switchTab(&#39;printers&#39;)">الطباعين</div>
            <div class="tab" onclick="switchTab(&#39;accounting&#39;)">💰 النظام المحاسبي</div>
            <div class="tab" onclick="switchTab(&#39;settings&#39;)">⚙️ الإعدادات</div>
        </div>

        <!-- المحتوى -->
        <div class="content">
            <div id="messages"></div>

            <!-- قسم الأعمال -->
            <div id="jobs-section" class="">
                <!-- شريط البحث -->
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="البحث في الأعمال..." id="searchInput">
                    <span class="search-icon">🔍</span>
                </div>

                <!-- الإحصائيات -->
                <div class="stats-container">
                    <div class="stat-card active" id="stat-all" onclick="filterByStatus(&#39;all&#39;)">
                        <div class="stat-number" id="totalJobs">5</div>
                        <div class="stat-label">إجمالي الأعمال</div>
                    </div>
                    <div class="stat-card" id="stat-pending" onclick="filterByStatus(&#39;pending&#39;)">
                        <div class="stat-number" id="pendingJobs">4</div>
                        <div class="stat-label">في الانتظار</div>
                    </div>
                    <div class="stat-card" id="stat-progress" onclick="filterByStatus(&#39;in-progress&#39;)">
                        <div class="stat-number" id="inProgressJobs">0</div>
                        <div class="stat-label">قيد الطباعة</div>
                    </div>
                    <div class="stat-card" id="stat-printed" onclick="filterByStatus(&#39;printed&#39;)">
                        <div class="stat-number" id="printedJobs">0</div>
                        <div class="stat-label">تم الطباعة</div>
                    </div>
                    <div class="stat-card" id="stat-completed" onclick="filterByStatus(&#39;completed&#39;)">
                        <div class="stat-number" id="completedJobs">1</div>
                        <div class="stat-label">مرحلة</div>
                    </div>
                </div>

                <!-- قائمة الأعمال -->
                <div id="jobsList" class="jobs-grid">
                <div class="job-card pending ">
                    <div class="job-header">
                        <div class="job-title">اكرم</div>
                        <div class="job-status status-pending">
                            في الانتظار 
                        </div>
                    </div>

                    <div class="job-details">
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">رقم الفاتورة:</span>
                            <span class="job-detail-value" style="color: #3b82f6; font-weight: bold;">7</span>
                        </div>
                        
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">الكمية الإجمالية:</span>
                            <span class="job-detail-value">غير محدد</span>
                        </div>
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">التاريخ:</span>
                            <span class="job-detail-value">٢٦‏/٦‏/٢٠٢٥</span>
                        </div>
                        
                    </div>

                    <!-- حقول التحديث المباشر -->
                    <div style="
                        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                        border: 2px solid #cbd5e1;
                        border-radius: 12px;
                        padding: 16px;
                        margin: 16px 0;
                        
                    ">
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            margin-bottom: 12px;
                        ">
                            <h4 style="
                                color: #1e293b;
                                margin: 0;
                                font-size: 14px;
                                font-weight: 600;
                            ">📝 تحديث سريع:</h4>
                            
                            <span style="
                                background: linear-gradient(135deg, #1e3a8a, #1e40af);
                                color: white;
                                padding: 8px 16px;
                                border-radius: 20px;
                                font-size: 36px;
                                font-weight: bold;
                                font-family: &#39;Courier New&#39;, monospace;
                                letter-spacing: 2px;
                                box-shadow: 0 4px 12px rgba(30, 58, 138, 0.4);
                                border: 2px solid #1e40af;
                                min-width: 50px;
                                text-align: center;
                            ">7</span>
                            
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                            <div>
                                <label style="
                                    display: block;
                                    margin-bottom: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: #64748b;
                                ">الكمية المطبوعة:</label>
                                <input type="text" id="quantity_1750892144584" value="" placeholder="الكمية المطبوعة" style="
                                        width: 100%;
                                        padding: 8px 12px;
                                        border: 2px solid #e5e7eb;
                                        border-radius: 6px;
                                        font-size: 14px;
                                        transition: all 0.3s ease;
                                    " onchange="quickUpdateJob(1750892144584, &#39;quantity&#39;, this.value)" oninput="quickUpdateJob(1750892144584, &#39;quantity&#39;, this.value)">
                            </div>

                            <div>
                                <label style="
                                    display: block;
                                    margin-bottom: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: #64748b;
                                ">اسم الطباع:</label>
                                <select id="printer_1750892144584" onchange="quickUpdateJob(1750892144584, &#39;printer&#39;, this.value)" style="
                                        width: 100%;
                                        padding: 8px 12px;
                                        border: 2px solid #e5e7eb;
                                        border-radius: 6px;
                                        font-size: 14px;
                                        transition: all 0.3s ease;
                                        background: white;
                                    ">
                                    <option value="">اختر الطباع...</option>
                                    <option value="أحمد محمد">أحمد محمد</option><option value="محمد علي">محمد علي</option><option value="سعد أحمد">سعد أحمد</option>
                                </select>
                            </div>
                        </div>

                        <!-- حقول إضافية تظهر بعد تسجيل الكمية والطباع -->
                        <div id="additional_fields_1750892144584" style="
                            display: none;
                        ">
                            <!-- حقل السعر - يظهر بعد تسجيل الكمية والطباع -->
                            <div id="price_section_1750892144584" style="display: none; margin-bottom: 12px;">
                                <div>
                                    <label style="
                                        display: block;
                                        margin-bottom: 4px;
                                        font-size: 12px;
                                        font-weight: 600;
                                        color: #64748b;
                                    ">سعر الوحدة (ريال يمني):</label>
                                    <input type="number" id="price_1750892144584" value="" placeholder="سعر الوحدة" min="0" step="0.01" style="
                                            width: 100%;
                                            padding: 8px 12px;
                                            border: 2px solid #e5e7eb;
                                            border-radius: 6px;
                                            font-size: 14px;
                                            transition: all 0.3s ease;
                                        " onchange="quickUpdateJob(1750892144584, &#39;unitPrice&#39;, this.value)" oninput="updateTotalAmount(1750892144584); checkAndShowCustomerField(1750892144584)">
                                </div>

                                <!-- عرض المبلغ الإجمالي -->
                                <div id="total_amount_1750892144584" style="
                                    margin-top: 8px;
                                    padding: 8px 12px;
                                    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
                                    border: 2px solid #10b981;
                                    border-radius: 6px;
                                    text-align: center;
                                    font-weight: bold;
                                    color: #065f46;
                                    display: none;
                                ">
                                    💰 المبلغ الإجمالي: 0 ريال يمني
                                </div>
                            </div>

                            <!-- حقل العميل - يظهر بعد تسجيل السعر -->
                            <div id="customer_section_1750892144584" style="display: none; margin-bottom: 12px;">
                                <div>
                                    <label style="
                                        display: block;
                                        margin-bottom: 4px;
                                        font-size: 12px;
                                        font-weight: 600;
                                        color: #64748b;
                                    ">اسم العميل:</label>
                                    <div style="position: relative;">
                                        <input type="text" id="customer_1750892144584" value="" placeholder="اسم العميل" style="
                                                width: 100%;
                                                padding: 8px 12px;
                                                border: 2px solid #e5e7eb;
                                                border-radius: 6px;
                                                font-size: 14px;
                                                transition: all 0.3s ease;
                                            " onchange="quickUpdateJob(1750892144584, &#39;customer&#39;, this.value)" oninput="showCustomerSuggestions(1750892144584, this.value)" onfocus="showCustomerSuggestions(1750892144584, this.value)" onblur="setTimeout(() =&gt; hideCustomerSuggestions(1750892144584), 200)">
                                        <div id="customer_suggestions_1750892144584" class="customer-suggestions" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button onclick="saveQuickUpdate(1750892144584)" style="
                                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 6px;
                                font-weight: 600;
                                font-size: 12px;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">💾 حفظ التحديث</button>
                        </div>
                    </div>

                    <div class="job-actions">
                        
                        <button class="btn btn-success" onclick="completeJob(1750892144584)">
                            📤 ترحيل
                        </button>
                        
                        
                        
                        <button class="btn btn-danger" onclick="deleteJob(1750892144584)">
                            🗑️ حذف
                        </button>
                        
                    </div>
                </div>
            
                <div class="job-card pending ">
                    <div class="job-header">
                        <div class="job-title">طباعة اكياس باسم محمد المحمدي0</div>
                        <div class="job-status status-pending">
                            في الانتظار 
                        </div>
                    </div>

                    <div class="job-details">
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">رقم الفاتورة:</span>
                            <span class="job-detail-value" style="color: #3b82f6; font-weight: bold;">6</span>
                        </div>
                        
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">الكمية الإجمالية:</span>
                            <span class="job-detail-value">غير محدد</span>
                        </div>
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">التاريخ:</span>
                            <span class="job-detail-value">٢٦‏/٦‏/٢٠٢٥</span>
                        </div>
                        
                    </div>

                    <!-- حقول التحديث المباشر -->
                    <div style="
                        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                        border: 2px solid #cbd5e1;
                        border-radius: 12px;
                        padding: 16px;
                        margin: 16px 0;
                        
                    ">
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            margin-bottom: 12px;
                        ">
                            <h4 style="
                                color: #1e293b;
                                margin: 0;
                                font-size: 14px;
                                font-weight: 600;
                            ">📝 تحديث سريع:</h4>
                            
                            <span style="
                                background: linear-gradient(135deg, #1e3a8a, #1e40af);
                                color: white;
                                padding: 8px 16px;
                                border-radius: 20px;
                                font-size: 36px;
                                font-weight: bold;
                                font-family: &#39;Courier New&#39;, monospace;
                                letter-spacing: 2px;
                                box-shadow: 0 4px 12px rgba(30, 58, 138, 0.4);
                                border: 2px solid #1e40af;
                                min-width: 50px;
                                text-align: center;
                            ">6</span>
                            
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                            <div>
                                <label style="
                                    display: block;
                                    margin-bottom: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: #64748b;
                                ">الكمية المطبوعة:</label>
                                <input type="text" id="quantity_1750891746137" value="" placeholder="الكمية المطبوعة" style="
                                        width: 100%;
                                        padding: 8px 12px;
                                        border: 2px solid #e5e7eb;
                                        border-radius: 6px;
                                        font-size: 14px;
                                        transition: all 0.3s ease;
                                    " onchange="quickUpdateJob(1750891746137, &#39;quantity&#39;, this.value)" oninput="quickUpdateJob(1750891746137, &#39;quantity&#39;, this.value)">
                            </div>

                            <div>
                                <label style="
                                    display: block;
                                    margin-bottom: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: #64748b;
                                ">اسم الطباع:</label>
                                <select id="printer_1750891746137" onchange="quickUpdateJob(1750891746137, &#39;printer&#39;, this.value)" style="
                                        width: 100%;
                                        padding: 8px 12px;
                                        border: 2px solid #e5e7eb;
                                        border-radius: 6px;
                                        font-size: 14px;
                                        transition: all 0.3s ease;
                                        background: white;
                                    ">
                                    <option value="">اختر الطباع...</option>
                                    <option value="أحمد محمد">أحمد محمد</option><option value="محمد علي">محمد علي</option><option value="سعد أحمد">سعد أحمد</option>
                                </select>
                            </div>
                        </div>

                        <!-- حقول إضافية تظهر بعد تسجيل الكمية والطباع -->
                        <div id="additional_fields_1750891746137" style="
                            display: none;
                        ">
                            <!-- حقل السعر - يظهر بعد تسجيل الكمية والطباع -->
                            <div id="price_section_1750891746137" style="display: none; margin-bottom: 12px;">
                                <div>
                                    <label style="
                                        display: block;
                                        margin-bottom: 4px;
                                        font-size: 12px;
                                        font-weight: 600;
                                        color: #64748b;
                                    ">سعر الوحدة (ريال يمني):</label>
                                    <input type="number" id="price_1750891746137" value="" placeholder="سعر الوحدة" min="0" step="0.01" style="
                                            width: 100%;
                                            padding: 8px 12px;
                                            border: 2px solid #e5e7eb;
                                            border-radius: 6px;
                                            font-size: 14px;
                                            transition: all 0.3s ease;
                                        " onchange="quickUpdateJob(1750891746137, &#39;unitPrice&#39;, this.value)" oninput="updateTotalAmount(1750891746137); checkAndShowCustomerField(1750891746137)">
                                </div>

                                <!-- عرض المبلغ الإجمالي -->
                                <div id="total_amount_1750891746137" style="
                                    margin-top: 8px;
                                    padding: 8px 12px;
                                    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
                                    border: 2px solid #10b981;
                                    border-radius: 6px;
                                    text-align: center;
                                    font-weight: bold;
                                    color: #065f46;
                                    display: none;
                                ">
                                    💰 المبلغ الإجمالي: 0 ريال يمني
                                </div>
                            </div>

                            <!-- حقل العميل - يظهر بعد تسجيل السعر -->
                            <div id="customer_section_1750891746137" style="display: none; margin-bottom: 12px;">
                                <div>
                                    <label style="
                                        display: block;
                                        margin-bottom: 4px;
                                        font-size: 12px;
                                        font-weight: 600;
                                        color: #64748b;
                                    ">اسم العميل:</label>
                                    <div style="position: relative;">
                                        <input type="text" id="customer_1750891746137" value="" placeholder="اسم العميل" style="
                                                width: 100%;
                                                padding: 8px 12px;
                                                border: 2px solid #e5e7eb;
                                                border-radius: 6px;
                                                font-size: 14px;
                                                transition: all 0.3s ease;
                                            " onchange="quickUpdateJob(1750891746137, &#39;customer&#39;, this.value)" oninput="showCustomerSuggestions(1750891746137, this.value)" onfocus="showCustomerSuggestions(1750891746137, this.value)" onblur="setTimeout(() =&gt; hideCustomerSuggestions(1750891746137), 200)">
                                        <div id="customer_suggestions_1750891746137" class="customer-suggestions" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button onclick="saveQuickUpdate(1750891746137)" style="
                                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 6px;
                                font-weight: 600;
                                font-size: 12px;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">💾 حفظ التحديث</button>
                        </div>
                    </div>

                    <div class="job-actions">
                        
                        <button class="btn btn-success" onclick="completeJob(1750891746137)">
                            📤 ترحيل
                        </button>
                        
                        
                        
                        <button class="btn btn-danger" onclick="deleteJob(1750891746137)">
                            🗑️ حذف
                        </button>
                        
                    </div>
                </div>
            
                <div class="job-card pending ">
                    <div class="job-header">
                        <div class="job-title">طباعة اكياس باسم محمد المحمدي</div>
                        <div class="job-status status-pending">
                            في الانتظار 
                        </div>
                    </div>

                    <div class="job-details">
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">رقم الفاتورة:</span>
                            <span class="job-detail-value" style="color: #3b82f6; font-weight: bold;">5</span>
                        </div>
                        
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">الكمية الإجمالية:</span>
                            <span class="job-detail-value">غير محدد</span>
                        </div>
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">التاريخ:</span>
                            <span class="job-detail-value">٢٦‏/٦‏/٢٠٢٥</span>
                        </div>
                        
                    </div>

                    <!-- حقول التحديث المباشر -->
                    <div style="
                        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                        border: 2px solid #cbd5e1;
                        border-radius: 12px;
                        padding: 16px;
                        margin: 16px 0;
                        
                    ">
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            margin-bottom: 12px;
                        ">
                            <h4 style="
                                color: #1e293b;
                                margin: 0;
                                font-size: 14px;
                                font-weight: 600;
                            ">📝 تحديث سريع:</h4>
                            
                            <span style="
                                background: linear-gradient(135deg, #1e3a8a, #1e40af);
                                color: white;
                                padding: 8px 16px;
                                border-radius: 20px;
                                font-size: 36px;
                                font-weight: bold;
                                font-family: &#39;Courier New&#39;, monospace;
                                letter-spacing: 2px;
                                box-shadow: 0 4px 12px rgba(30, 58, 138, 0.4);
                                border: 2px solid #1e40af;
                                min-width: 50px;
                                text-align: center;
                            ">5</span>
                            
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                            <div>
                                <label style="
                                    display: block;
                                    margin-bottom: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: #64748b;
                                ">الكمية المطبوعة:</label>
                                <input type="text" id="quantity_1750891726225" value="" placeholder="الكمية المطبوعة" style="
                                        width: 100%;
                                        padding: 8px 12px;
                                        border: 2px solid #e5e7eb;
                                        border-radius: 6px;
                                        font-size: 14px;
                                        transition: all 0.3s ease;
                                    " onchange="quickUpdateJob(1750891726225, &#39;quantity&#39;, this.value)" oninput="quickUpdateJob(1750891726225, &#39;quantity&#39;, this.value)">
                            </div>

                            <div>
                                <label style="
                                    display: block;
                                    margin-bottom: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: #64748b;
                                ">اسم الطباع:</label>
                                <select id="printer_1750891726225" onchange="quickUpdateJob(1750891726225, &#39;printer&#39;, this.value)" style="
                                        width: 100%;
                                        padding: 8px 12px;
                                        border: 2px solid #e5e7eb;
                                        border-radius: 6px;
                                        font-size: 14px;
                                        transition: all 0.3s ease;
                                        background: white;
                                    ">
                                    <option value="">اختر الطباع...</option>
                                    <option value="أحمد محمد">أحمد محمد</option><option value="محمد علي">محمد علي</option><option value="سعد أحمد">سعد أحمد</option>
                                </select>
                            </div>
                        </div>

                        <!-- حقول إضافية تظهر بعد تسجيل الكمية والطباع -->
                        <div id="additional_fields_1750891726225" style="
                            display: none;
                        ">
                            <!-- حقل السعر - يظهر بعد تسجيل الكمية والطباع -->
                            <div id="price_section_1750891726225" style="display: none; margin-bottom: 12px;">
                                <div>
                                    <label style="
                                        display: block;
                                        margin-bottom: 4px;
                                        font-size: 12px;
                                        font-weight: 600;
                                        color: #64748b;
                                    ">سعر الوحدة (ريال يمني):</label>
                                    <input type="number" id="price_1750891726225" value="" placeholder="سعر الوحدة" min="0" step="0.01" style="
                                            width: 100%;
                                            padding: 8px 12px;
                                            border: 2px solid #e5e7eb;
                                            border-radius: 6px;
                                            font-size: 14px;
                                            transition: all 0.3s ease;
                                        " onchange="quickUpdateJob(1750891726225, &#39;unitPrice&#39;, this.value)" oninput="updateTotalAmount(1750891726225); checkAndShowCustomerField(1750891726225)">
                                </div>

                                <!-- عرض المبلغ الإجمالي -->
                                <div id="total_amount_1750891726225" style="
                                    margin-top: 8px;
                                    padding: 8px 12px;
                                    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
                                    border: 2px solid #10b981;
                                    border-radius: 6px;
                                    text-align: center;
                                    font-weight: bold;
                                    color: #065f46;
                                    display: none;
                                ">
                                    💰 المبلغ الإجمالي: 0 ريال يمني
                                </div>
                            </div>

                            <!-- حقل العميل - يظهر بعد تسجيل السعر -->
                            <div id="customer_section_1750891726225" style="display: none; margin-bottom: 12px;">
                                <div>
                                    <label style="
                                        display: block;
                                        margin-bottom: 4px;
                                        font-size: 12px;
                                        font-weight: 600;
                                        color: #64748b;
                                    ">اسم العميل:</label>
                                    <div style="position: relative;">
                                        <input type="text" id="customer_1750891726225" value="" placeholder="اسم العميل" style="
                                                width: 100%;
                                                padding: 8px 12px;
                                                border: 2px solid #e5e7eb;
                                                border-radius: 6px;
                                                font-size: 14px;
                                                transition: all 0.3s ease;
                                            " onchange="quickUpdateJob(1750891726225, &#39;customer&#39;, this.value)" oninput="showCustomerSuggestions(1750891726225, this.value)" onfocus="showCustomerSuggestions(1750891726225, this.value)" onblur="setTimeout(() =&gt; hideCustomerSuggestions(1750891726225), 200)">
                                        <div id="customer_suggestions_1750891726225" class="customer-suggestions" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button onclick="saveQuickUpdate(1750891726225)" style="
                                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 6px;
                                font-weight: 600;
                                font-size: 12px;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">💾 حفظ التحديث</button>
                        </div>
                    </div>

                    <div class="job-actions">
                        
                        <button class="btn btn-success" onclick="completeJob(1750891726225)">
                            📤 ترحيل
                        </button>
                        
                        
                        
                        <button class="btn btn-danger" onclick="deleteJob(1750891726225)">
                            🗑️ حذف
                        </button>
                        
                    </div>
                </div>
            
                <div class="job-card completed ">
                    <div class="job-header">
                        <div class="job-title">طباعة اكياس باسم محمد المحمدي</div>
                        <div class="job-status status-completed">
                            مكتمل 
                        </div>
                    </div>

                    <div class="job-details">
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">رقم الفاتورة:</span>
                            <span class="job-detail-value" style="color: #3b82f6; font-weight: bold;">4</span>
                        </div>
                        
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">العميل:</span>
                            <span class="job-detail-value" style="color: #059669; font-weight: bold;">مريم</span>
                        </div>
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">الكمية الإجمالية:</span>
                            <span class="job-detail-value">غير محدد</span>
                        </div>
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">التاريخ:</span>
                            <span class="job-detail-value">٢٦‏/٦‏/٢٠٢٥</span>
                        </div>
                        
                    </div>

                    <!-- حقول التحديث المباشر -->
                    <div style="
                        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                        border: 2px solid #cbd5e1;
                        border-radius: 12px;
                        padding: 16px;
                        margin: 16px 0;
                        border-color: #10b981; background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
                    ">
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            margin-bottom: 12px;
                        ">
                            <h4 style="
                                color: #1e293b;
                                margin: 0;
                                font-size: 14px;
                                font-weight: 600;
                            ">✅ العمل مرحل - قابل للتعديل:</h4>
                            
                            <span style="
                                background: linear-gradient(135deg, #1e3a8a, #1e40af);
                                color: white;
                                padding: 8px 16px;
                                border-radius: 20px;
                                font-size: 36px;
                                font-weight: bold;
                                font-family: &#39;Courier New&#39;, monospace;
                                letter-spacing: 2px;
                                box-shadow: 0 4px 12px rgba(30, 58, 138, 0.4);
                                border: 2px solid #1e40af;
                                min-width: 50px;
                                text-align: center;
                            ">4</span>
                            
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                            <div>
                                <label style="
                                    display: block;
                                    margin-bottom: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: #64748b;
                                ">الكمية المطبوعة:</label>
                                <input type="text" id="quantity_1750891456239" value="900" placeholder="الكمية المطبوعة" style="
                                        width: 100%;
                                        padding: 8px 12px;
                                        border: 2px solid #e5e7eb;
                                        border-radius: 6px;
                                        font-size: 14px;
                                        transition: all 0.3s ease;
                                    " onchange="quickUpdateJob(1750891456239, &#39;quantity&#39;, this.value)" oninput="quickUpdateJob(1750891456239, &#39;quantity&#39;, this.value)">
                            </div>

                            <div>
                                <label style="
                                    display: block;
                                    margin-bottom: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: #64748b;
                                ">اسم الطباع:</label>
                                <select id="printer_1750891456239" onchange="quickUpdateJob(1750891456239, &#39;printer&#39;, this.value)" style="
                                        width: 100%;
                                        padding: 8px 12px;
                                        border: 2px solid #e5e7eb;
                                        border-radius: 6px;
                                        font-size: 14px;
                                        transition: all 0.3s ease;
                                        background: white;
                                    ">
                                    <option value="">اختر الطباع...</option>
                                    <option value="أحمد محمد" selected="">أحمد محمد</option><option value="محمد علي">محمد علي</option><option value="سعد أحمد">سعد أحمد</option>
                                </select>
                            </div>
                        </div>

                        <!-- حقول إضافية تظهر بعد تسجيل الكمية والطباع -->
                        <div id="additional_fields_1750891456239" style="
                            display: block;
                        ">
                            <!-- حقل السعر - يظهر بعد تسجيل الكمية والطباع -->
                            <div id="price_section_1750891456239" style="display: block; margin-bottom: 12px;">
                                <div>
                                    <label style="
                                        display: block;
                                        margin-bottom: 4px;
                                        font-size: 12px;
                                        font-weight: 600;
                                        color: #64748b;
                                    ">سعر الوحدة (ريال يمني):</label>
                                    <input type="number" id="price_1750891456239" value="800" placeholder="سعر الوحدة" min="0" step="0.01" style="
                                            width: 100%;
                                            padding: 8px 12px;
                                            border: 2px solid #e5e7eb;
                                            border-radius: 6px;
                                            font-size: 14px;
                                            transition: all 0.3s ease;
                                        " onchange="quickUpdateJob(1750891456239, &#39;unitPrice&#39;, this.value)" oninput="updateTotalAmount(1750891456239); checkAndShowCustomerField(1750891456239)">
                                </div>

                                <!-- عرض المبلغ الإجمالي -->
                                <div id="total_amount_1750891456239" style="
                                    margin-top: 8px;
                                    padding: 8px 12px;
                                    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
                                    border: 2px solid #10b981;
                                    border-radius: 6px;
                                    text-align: center;
                                    font-weight: bold;
                                    color: #065f46;
                                    display: block;
                                ">
                                    💰 المبلغ الإجمالي: 720,000 ريال يمني
                                </div>
                            </div>

                            <!-- حقل العميل - يظهر بعد تسجيل السعر -->
                            <div id="customer_section_1750891456239" style="display: block; margin-bottom: 12px;">
                                <div>
                                    <label style="
                                        display: block;
                                        margin-bottom: 4px;
                                        font-size: 12px;
                                        font-weight: 600;
                                        color: #64748b;
                                    ">اسم العميل:</label>
                                    <div style="position: relative;">
                                        <input type="text" id="customer_1750891456239" value="مريم" placeholder="اسم العميل" style="
                                                width: 100%;
                                                padding: 8px 12px;
                                                border: 2px solid #e5e7eb;
                                                border-radius: 6px;
                                                font-size: 14px;
                                                transition: all 0.3s ease;
                                            " onchange="quickUpdateJob(1750891456239, &#39;customer&#39;, this.value)" oninput="showCustomerSuggestions(1750891456239, this.value)" onfocus="showCustomerSuggestions(1750891456239, this.value)" onblur="setTimeout(() =&gt; hideCustomerSuggestions(1750891456239), 200)">
                                        <div id="customer_suggestions_1750891456239" class="customer-suggestions" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button onclick="saveQuickUpdate(1750891456239)" style="
                                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 6px;
                                font-weight: 600;
                                font-size: 12px;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">💾 حفظ التحديث</button>
                        </div>
                    </div>

                    <div class="job-actions">
                        
                        <button class="btn btn-primary" onclick="enableEditMode(1750891456239)" style="
                            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 8px;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            margin-left: 8px;
                        ">
                            ✏️ تعديل العمل
                        </button>
                        <div style="
                            background: linear-gradient(135deg, #10b981, #059669);
                            color: white;
                            padding: 8px 16px;
                            border-radius: 8px;
                            text-align: center;
                            font-weight: 600;
                            font-size: 14px;
                            display: inline-block;
                        ">
                            📤 مرحل
                        </div>
                        
                        
                        <button class="btn btn-secondary" onclick="editInvoiceFromJobsList(&#39;4&#39;)" style="margin-left: 8px;">
                            ✏️ تعديل الفاتورة
                        </button>
                        <button onclick="generateInvoicePDF(&#39;4&#39;)" style="
                            background: linear-gradient(135deg, #dc2626, #b91c1c);
                            color: white;
                            border: none;
                            padding: 6px 12px;
                            border-radius: 6px;
                            font-size: 12px;
                            cursor: pointer;
                            margin-left: 8px;
                        ">📄 تحميل PDF</button>
                        <button onclick="sendInvoicePDFWhatsApp(&#39;4&#39;)" style="
                            background: linear-gradient(135deg, #25d366, #128c7e);
                            color: white;
                            border: none;
                            padding: 6px 12px;
                            border-radius: 6px;
                            font-size: 12px;
                            cursor: pointer;
                            margin-left: 8px;
                        ">📱 PDF واتساب</button>
                        
                        
                        <button class="btn btn-danger" onclick="confirmDeleteCompleted(1750891456239)" style="opacity: 0.7;">
                            🗑️ حذف نهائي
                        </button>
                        
                    </div>
                </div>
            
                <div class="job-card pending ">
                    <div class="job-header">
                        <div class="job-title">طباعة كروت شخصية</div>
                        <div class="job-status status-pending">
                            في الانتظار 
                        </div>
                    </div>

                    <div class="job-details">
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">رقم الفاتورة:</span>
                            <span class="job-detail-value" style="color: #3b82f6; font-weight: bold;">INV-2024-003</span>
                        </div>
                        
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">العميل:</span>
                            <span class="job-detail-value" style="color: #059669; font-weight: bold;">مكتب المحاماة</span>
                        </div>
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">الكمية الإجمالية:</span>
                            <span class="job-detail-value">500</span>
                        </div>
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">تفاصيل العمل:</span>
                            <span class="job-detail-value">كروت شخصية للمحامين - لون واحد - ورق مقوى</span>
                        </div>
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">التاريخ:</span>
                            <span class="job-detail-value">٢٦‏/٦‏/٢٠٢٥</span>
                        </div>
                        
                    </div>

                    <!-- حقول التحديث المباشر -->
                    <div style="
                        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                        border: 2px solid #cbd5e1;
                        border-radius: 12px;
                        padding: 16px;
                        margin: 16px 0;
                        
                    ">
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            margin-bottom: 12px;
                        ">
                            <h4 style="
                                color: #1e293b;
                                margin: 0;
                                font-size: 14px;
                                font-weight: 600;
                            ">📝 تحديث سريع:</h4>
                            
                            <span style="
                                background: linear-gradient(135deg, #1e3a8a, #1e40af);
                                color: white;
                                padding: 8px 16px;
                                border-radius: 20px;
                                font-size: 36px;
                                font-weight: bold;
                                font-family: &#39;Courier New&#39;, monospace;
                                letter-spacing: 2px;
                                box-shadow: 0 4px 12px rgba(30, 58, 138, 0.4);
                                border: 2px solid #1e40af;
                                min-width: 50px;
                                text-align: center;
                            ">3</span>
                            
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                            <div>
                                <label style="
                                    display: block;
                                    margin-bottom: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: #64748b;
                                ">الكمية المطبوعة:</label>
                                <input type="text" id="quantity_3" value="" placeholder="الكمية المطبوعة" style="
                                        width: 100%;
                                        padding: 8px 12px;
                                        border: 2px solid #e5e7eb;
                                        border-radius: 6px;
                                        font-size: 14px;
                                        transition: all 0.3s ease;
                                    " onchange="quickUpdateJob(3, &#39;quantity&#39;, this.value)" oninput="quickUpdateJob(3, &#39;quantity&#39;, this.value)">
                            </div>

                            <div>
                                <label style="
                                    display: block;
                                    margin-bottom: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: #64748b;
                                ">اسم الطباع:</label>
                                <select id="printer_3" onchange="quickUpdateJob(3, &#39;printer&#39;, this.value)" style="
                                        width: 100%;
                                        padding: 8px 12px;
                                        border: 2px solid #e5e7eb;
                                        border-radius: 6px;
                                        font-size: 14px;
                                        transition: all 0.3s ease;
                                        background: white;
                                    ">
                                    <option value="">اختر الطباع...</option>
                                    <option value="أحمد محمد">أحمد محمد</option><option value="محمد علي">محمد علي</option><option value="سعد أحمد" selected="">سعد أحمد</option>
                                </select>
                            </div>
                        </div>

                        <!-- حقول إضافية تظهر بعد تسجيل الكمية والطباع -->
                        <div id="additional_fields_3" style="
                            display: none;
                        ">
                            <!-- حقل السعر - يظهر بعد تسجيل الكمية والطباع -->
                            <div id="price_section_3" style="display: block; margin-bottom: 12px;">
                                <div>
                                    <label style="
                                        display: block;
                                        margin-bottom: 4px;
                                        font-size: 12px;
                                        font-weight: 600;
                                        color: #64748b;
                                    ">سعر الوحدة (ريال يمني):</label>
                                    <input type="number" id="price_3" value="" placeholder="سعر الوحدة" min="0" step="0.01" style="
                                            width: 100%;
                                            padding: 8px 12px;
                                            border: 2px solid #e5e7eb;
                                            border-radius: 6px;
                                            font-size: 14px;
                                            transition: all 0.3s ease;
                                        " onchange="quickUpdateJob(3, &#39;unitPrice&#39;, this.value)" oninput="updateTotalAmount(3); checkAndShowCustomerField(3)">
                                </div>

                                <!-- عرض المبلغ الإجمالي -->
                                <div id="total_amount_3" style="
                                    margin-top: 8px;
                                    padding: 8px 12px;
                                    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
                                    border: 2px solid #10b981;
                                    border-radius: 6px;
                                    text-align: center;
                                    font-weight: bold;
                                    color: #065f46;
                                    display: none;
                                ">
                                    💰 المبلغ الإجمالي: 0 ريال يمني
                                </div>
                            </div>

                            <!-- حقل العميل - يظهر بعد تسجيل السعر -->
                            <div id="customer_section_3" style="display: none; margin-bottom: 12px;">
                                <div>
                                    <label style="
                                        display: block;
                                        margin-bottom: 4px;
                                        font-size: 12px;
                                        font-weight: 600;
                                        color: #64748b;
                                    ">اسم العميل:</label>
                                    <div style="position: relative;">
                                        <input type="text" id="customer_3" value="مكتب المحاماة" placeholder="اسم العميل" style="
                                                width: 100%;
                                                padding: 8px 12px;
                                                border: 2px solid #e5e7eb;
                                                border-radius: 6px;
                                                font-size: 14px;
                                                transition: all 0.3s ease;
                                            " onchange="quickUpdateJob(3, &#39;customer&#39;, this.value)" oninput="showCustomerSuggestions(3, this.value)" onfocus="showCustomerSuggestions(3, this.value)" onblur="setTimeout(() =&gt; hideCustomerSuggestions(3), 200)">
                                        <div id="customer_suggestions_3" class="customer-suggestions" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button onclick="saveQuickUpdate(3)" style="
                                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 6px;
                                font-weight: 600;
                                font-size: 12px;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">💾 حفظ التحديث</button>
                        </div>
                    </div>

                    <div class="job-actions">
                        
                        <button class="btn btn-success" onclick="completeJob(3)">
                            📤 ترحيل
                        </button>
                        
                        
                        
                        <button class="btn btn-danger" onclick="deleteJob(3)">
                            🗑️ حذف
                        </button>
                        
                    </div>
                </div>
            </div>
            </div>

            <!-- قسم إضافة عمل -->
            <div id="add-job-section" class="hidden">
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 20px;
                    padding: 2px;
                    margin-bottom: 20px;
                    box-shadow: 0 10px 40px rgba(102, 126, 234, 0.3);
                ">
                    <div style="
                        background: white;
                        border-radius: 18px;
                        padding: 40px;
                        position: relative;
                        overflow: hidden;
                    ">
                        <!-- خلفية زخرفية -->
                        <div style="
                            position: absolute;
                            top: -50px;
                            right: -50px;
                            width: 150px;
                            height: 150px;
                            background: linear-gradient(135deg, #667eea20, #764ba220);
                            border-radius: 50%;
                            z-index: 0;
                        "></div>
                        <div style="
                            position: absolute;
                            bottom: -30px;
                            left: -30px;
                            width: 100px;
                            height: 100px;
                            background: linear-gradient(135deg, #f093fb20, #f5576c20);
                            border-radius: 50%;
                            z-index: 0;
                        "></div>

                        <div style="position: relative; z-index: 1;">
                            <div style="text-align: center; margin-bottom: 40px;">
                                <div style="
                                    display: inline-block;
                                    background: linear-gradient(135deg, #667eea, #764ba2);
                                    color: white;
                                    padding: 15px 25px;
                                    border-radius: 50px;
                                    font-size: 24px;
                                    font-weight: bold;
                                    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
                                    margin-bottom: 15px;
                                ">
                                    🖨️ إضافة عمل طباعة جديد
                                </div>
                                <div style="
                                    display: inline-block;
                                    background: linear-gradient(135deg, #1e3a8a, #1e40af);
                                    color: white;
                                    padding: 8px 20px;
                                    border-radius: 25px;
                                    font-size: 18px;
                                    font-weight: bold;
                                    font-family: monospace;
                                    margin-bottom: 10px;
                                    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.4);
                                ">
                                    رقم العمل: <span id="nextJobNumber">8</span>
                                </div>
                                <p style="
                                    color: #64748b;
                                    font-size: 16px;
                                    margin: 0;
                                ">أدخل تفاصيل العمل الجديد</p>
                            </div>

                            <form id="jobForm" class="active">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px; margin-bottom: 25px;">
                                    <div class="enhanced-form-group">
                                        <label style="
                                            display: block;
                                            margin-bottom: 10px;
                                            font-weight: 700;
                                            color: #374151;
                                            font-size: 16px;
                                        ">📝 اسم العمل *</label>
                                        <input type="text" id="jobName" required="" style="width: 100%; padding: 15px 20px; border: 2px solid rgb(229, 231, 235); border-radius: 12px; font-size: 16px; transition: 0.3s; background: rgb(249, 250, 251); box-shadow: none;" placeholder="مثال: طباعة بروشورات" onfocus="this.style.borderColor=&#39;#667eea&#39;; this.style.background=&#39;white&#39;; this.style.boxShadow=&#39;0 0 0 3px rgba(102, 126, 234, 0.1)&#39;" onblur="this.style.borderColor=&#39;#e5e7eb&#39;; this.style.background=&#39;#f9fafb&#39;; this.style.boxShadow=&#39;none&#39;" oninput="checkForDuplicateInvoice()">

                                        <!-- حقول إضافية للأعمال المتعددة بنفس الفاتورة -->
                                        <div id="additionalJobsContainer" style="display: none; margin-top: 15px;">
                                            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border: 2px solid #f59e0b; border-radius: 10px; padding: 15px; margin-bottom: 15px;">
                                                <h4 style="margin: 0 0 10px 0; color: #92400e; font-size: 14px;">⚡ تم اكتشاف أعمال أخرى بنفس رقم الفاتورة</h4>
                                                <p style="margin: 0; color: #92400e; font-size: 12px;">يمكنك إضافة أعمال إضافية لنفس الفاتورة</p>
                                            </div>
                                            <div id="additionalJobsList"></div>
                                            <button type="button" onclick="addAdditionalJob()" style="
                                                background: linear-gradient(135deg, #3b82f6, #2563eb);
                                                color: white;
                                                border: none;
                                                padding: 10px 20px;
                                                border-radius: 8px;
                                                font-weight: 600;
                                                cursor: pointer;
                                                font-size: 14px;
                                                margin-top: 10px;
                                            ">➕ إضافة عمل آخر</button>
                                        </div>
                                    </div>

                                    <div class="enhanced-form-group">
                                        <label style="
                                            display: block;
                                            margin-bottom: 10px;
                                            font-weight: 700;
                                            color: #374151;
                                            font-size: 16px;
                                        ">🧾 رقم الفاتورة</label>
                                        <input type="text" id="invoiceNumber" readonly="" style="
                                            width: 100%;
                                            padding: 15px 20px;
                                            border: 2px solid #10b981;
                                            border-radius: 12px;
                                            font-size: 16px;
                                            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
                                            color: #065f46;
                                            font-weight: bold;
                                            text-align: center;
                                        " placeholder="رقم الفاتورة: 8">
                                    </div>

                                    <!-- زر الحفظ السريع بعد اسم العمل مباشرة -->
                                    <div style="
                                        display: flex;
                                        gap: 15px;
                                        justify-content: center;
                                        margin: 25px 0;
                                        padding: 20px;
                                        background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
                                        border-radius: 15px;
                                        border: 2px solid #0ea5e9;
                                    ">
                                        <button type="submit" style="background: linear-gradient(135deg, rgb(16, 185, 129), rgb(5, 150, 105)); color: white; border: none; padding: 15px 35px; border-radius: 50px; font-weight: 700; font-size: 18px; cursor: pointer; transition: 0.3s; box-shadow: rgba(16, 185, 129, 0.4) 0px 8px 25px; min-width: 180px; transform: translateY(0px);" onmouseover="this.style.transform=&#39;translateY(-2px)&#39;; this.style.boxShadow=&#39;0 12px 35px rgba(16, 185, 129, 0.5)&#39;" onmouseout="this.style.transform=&#39;translateY(0)&#39;; this.style.boxShadow=&#39;0 8px 25px rgba(16, 185, 129, 0.4)&#39;">
                                            💾 حفظ العمل
                                        </button>

                                        <button type="button" onclick="clearForm()" style="
                                            background: linear-gradient(135deg, #ef4444, #dc2626);
                                            color: white;
                                            border: none;
                                            padding: 15px 35px;
                                            border-radius: 50px;
                                            font-weight: 700;
                                            font-size: 18px;
                                            cursor: pointer;
                                            transition: all 0.3s ease;
                                            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
                                            min-width: 180px;
                                        " onmouseover="this.style.transform=&#39;translateY(-2px)&#39;; this.style.boxShadow=&#39;0 12px 35px rgba(239, 68, 68, 0.5)&#39;" onmouseout="this.style.transform=&#39;translateY(0)&#39;; this.style.boxShadow=&#39;0 8px 25px rgba(239, 68, 68, 0.4)&#39;">
                                            🗑️ مسح النموذج
                                        </button>
                                    </div>

                                    <div class="enhanced-form-group">
                                        <label style="
                                            display: block;
                                            margin-bottom: 10px;
                                            font-weight: 700;
                                            color: #374151;
                                            font-size: 16px;
                                        ">📋 تفاصيل العمل</label>
                                        <textarea id="jobDetails" style="
                                            width: 100%;
                                            padding: 15px 20px;
                                            border: 2px solid #e5e7eb;
                                            border-radius: 12px;
                                            font-size: 16px;
                                            transition: all 0.3s ease;
                                            background: #f9fafb;
                                            min-height: 120px;
                                            resize: vertical;
                                            font-family: inherit;
                                        " placeholder="مثال: طباعة بروشورات - 4 ألوان - ورق مطفي - مقاس A4" onfocus="this.style.borderColor=&#39;#667eea&#39;; this.style.background=&#39;white&#39;; this.style.boxShadow=&#39;0 0 0 3px rgba(102, 126, 234, 0.1)&#39;" onblur="this.style.borderColor=&#39;#e5e7eb&#39;; this.style.background=&#39;#f9fafb&#39;; this.style.boxShadow=&#39;none&#39;"></textarea>
                                    </div>
                                </div>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px; margin-bottom: 25px;">
                                    <div class="enhanced-form-group">
                                        <label style="
                                            display: block;
                                            margin-bottom: 10px;
                                            font-weight: 700;
                                            color: #374151;
                                            font-size: 16px;
                                        ">👤 اسم الطباع</label>
                                        <select id="printerNameSelect" style="
                                            width: 100%;
                                            padding: 15px 20px;
                                            border: 2px solid #e5e7eb;
                                            border-radius: 12px;
                                            font-size: 16px;
                                            transition: all 0.3s ease;
                                            background: #f9fafb;
                                        " onfocus="this.style.borderColor=&#39;#667eea&#39;; this.style.background=&#39;white&#39;; this.style.boxShadow=&#39;0 0 0 3px rgba(102, 126, 234, 0.1)&#39;" onblur="this.style.borderColor=&#39;#e5e7eb&#39;; this.style.background=&#39;#f9fafb&#39;; this.style.boxShadow=&#39;none&#39;"><option value="">اختر الطباع...</option><option value="أحمد محمد">أحمد محمد</option><option value="محمد علي">محمد علي</option><option value="سعد أحمد">سعد أحمد</option></select>
                                    </div>

                                    <div class="enhanced-form-group">
                                        <label style="
                                            display: block;
                                            margin-bottom: 10px;
                                            font-weight: 700;
                                            color: #374151;
                                            font-size: 16px;
                                        ">📊 الكمية المطلوبة</label>
                                        <input type="text" id="totalQuantity" style="
                                            width: 100%;
                                            padding: 15px 20px;
                                            border: 2px solid #e5e7eb;
                                            border-radius: 12px;
                                            font-size: 16px;
                                            transition: all 0.3s ease;
                                            background: #f9fafb;
                                        " placeholder="مثال: 1000 قطعة" onfocus="this.style.borderColor=&#39;#667eea&#39;; this.style.background=&#39;white&#39;; this.style.boxShadow=&#39;0 0 0 3px rgba(102, 126, 234, 0.1)&#39;" onblur="this.style.borderColor=&#39;#e5e7eb&#39;; this.style.background=&#39;#f9fafb&#39;; this.style.boxShadow=&#39;none&#39;">
                                    </div>
                                </div>

                                <div style="
                                    background: linear-gradient(135deg, #fef3c7, #fde68a);
                                    border: 2px solid #f59e0b;
                                    border-radius: 15px;
                                    padding: 20px;
                                    margin-bottom: 30px;
                                ">
                                    <div style="
                                        display: flex;
                                        align-items: center;
                                        gap: 15px;
                                    ">
                                        <input type="checkbox" id="isUrgent" style="
                                            width: 20px;
                                            height: 20px;
                                            accent-color: #f59e0b;
                                            cursor: pointer;
                                        ">
                                        <label for="isUrgent" style="
                                            font-size: 18px;
                                            font-weight: 700;
                                            color: #92400e;
                                            cursor: pointer;
                                            user-select: none;
                                        ">⚡ عمل مستعجل</label>
                                    </div>
                                </div>

                                <!-- قسم رفع الصور -->
                                <div style="
                                    background: linear-gradient(135deg, #e0f2fe, #b3e5fc);
                                    border: 2px solid #0288d1;
                                    border-radius: 15px;
                                    padding: 20px;
                                    margin-bottom: 30px;
                                ">
                                    <div style="
                                        display: flex;
                                        align-items: center;
                                        gap: 15px;
                                        margin-bottom: 15px;
                                    ">
                                        <h4 style="
                                            margin: 0;
                                            font-size: 16px;
                                            font-weight: 700;
                                            color: #01579b;
                                        ">📷 إضافة صور للعمل</h4>
                                    </div>

                                    <div style="
                                        display: grid;
                                        grid-template-columns: 1fr 1fr;
                                        gap: 15px;
                                        margin-bottom: 15px;
                                    ">
                                        <button type="button" onclick="openCamera()" style="
                                            background: linear-gradient(135deg, #0288d1, #0277bd);
                                            color: white;
                                            border: none;
                                            padding: 12px 20px;
                                            border-radius: 10px;
                                            font-weight: 600;
                                            cursor: pointer;
                                            transition: all 0.3s ease;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            gap: 8px;
                                        " onmouseover="this.style.transform=&#39;translateY(-2px)&#39;" onmouseout="this.style.transform=&#39;translateY(0)&#39;">
                                            📸 فتح الكاميرا
                                        </button>

                                        <label for="imageUpload" style="
                                            background: linear-gradient(135deg, #00acc1, #0097a7);
                                            color: white;
                                            border: none;
                                            padding: 12px 20px;
                                            border-radius: 10px;
                                            font-weight: 600;
                                            cursor: pointer;
                                            transition: all 0.3s ease;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            gap: 8px;
                                        " onmouseover="this.style.transform=&#39;translateY(-2px)&#39;" onmouseout="this.style.transform=&#39;translateY(0)&#39;">
                                            🖼️ رفع صورة
                                        </label>
                                        <input type="file" id="imageUpload" accept="image/*" multiple="" style="display: none;" onchange="handleImageUpload(event)">
                                    </div>

                                    <!-- منطقة عرض الصور -->
                                    <div id="imagePreview" style="
                                        display: none;
                                        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                                        gap: 10px;
                                        margin-top: 15px;
                                        padding: 15px;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #b3e5fc;
                                    "></div>
                                </div>


                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم الطباعين -->
            <div id="printers-section" class="hidden">
                <!-- إضافة طباع جديد -->
                <div class="form-section">
                    <h2 style="margin-bottom: 24px; color: #1e293b; text-align: center;">➕ إضافة طباع جديد</h2>

                    <form id="printerForm">
                        <div class="form-group">
                            <label class="form-label">اسم الطباع *</label>
                            <input type="text" class="form-input" id="printerName" placeholder="مثال: أحمد محمد" required="">
                        </div>

                        <div class="form-group">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-input" id="printerPhone" placeholder="مثال: 01234567890">
                        </div>

                        <div class="form-group">
                            <label class="form-label">التخصص</label>
                            <input type="text" class="form-input" id="printerSpecialty" placeholder="مثال: طباعة ديجيتال، أوفست">
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">👤 إضافة الطباع</button>
                            <button type="button" class="btn btn-danger" onclick="clearPrinterForm()">🗑️ مسح النموذج</button>
                        </div>
                    </form>
                </div>

                <!-- قائمة الطباعين -->
                <div class="form-section">
                    <h2 style="margin-bottom: 24px; color: #1e293b; text-align: center;">👥 قائمة الطباعين</h2>
                    <div id="printersList">
                <div class="job-card" style="margin-bottom: 15px;">
                    <div class="job-header">
                        <div class="job-title">👤 أحمد محمد</div>
                        <div class="job-status status-completed">نشط</div>
                    </div>

                    <div class="job-details">
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">الهاتف:</span>
                            <span class="job-detail-value">01234567890</span>
                        </div>
                        
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">التخصص:</span>
                            <span class="job-detail-value">طباعة ديجيتال</span>
                        </div>
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">تاريخ الإضافة:</span>
                            <span class="job-detail-value">٢٦‏/٦‏/٢٠٢٥</span>
                        </div>
                    </div>

                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="editPrinter(1)">
                            📝 تعديل
                        </button>
                        <button class="btn btn-danger" onclick="deletePrinter(1)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            
                <div class="job-card" style="margin-bottom: 15px;">
                    <div class="job-header">
                        <div class="job-title">👤 محمد علي</div>
                        <div class="job-status status-completed">نشط</div>
                    </div>

                    <div class="job-details">
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">الهاتف:</span>
                            <span class="job-detail-value">01098765432</span>
                        </div>
                        
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">التخصص:</span>
                            <span class="job-detail-value">طباعة أوفست</span>
                        </div>
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">تاريخ الإضافة:</span>
                            <span class="job-detail-value">٢٦‏/٦‏/٢٠٢٥</span>
                        </div>
                    </div>

                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="editPrinter(2)">
                            📝 تعديل
                        </button>
                        <button class="btn btn-danger" onclick="deletePrinter(2)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            
                <div class="job-card" style="margin-bottom: 15px;">
                    <div class="job-header">
                        <div class="job-title">👤 سعد أحمد</div>
                        <div class="job-status status-completed">نشط</div>
                    </div>

                    <div class="job-details">
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">الهاتف:</span>
                            <span class="job-detail-value">01555666777</span>
                        </div>
                        
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">التخصص:</span>
                            <span class="job-detail-value">طباعة كبيرة الحجم</span>
                        </div>
                        
                        <div class="job-detail-row">
                            <span class="job-detail-label">تاريخ الإضافة:</span>
                            <span class="job-detail-value">٢٦‏/٦‏/٢٠٢٥</span>
                        </div>
                    </div>

                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="editPrinter(3)">
                            📝 تعديل
                        </button>
                        <button class="btn btn-danger" onclick="deletePrinter(3)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            </div>
                </div>
            </div>

            <!-- قسم النظام المحاسبي -->
            <div id="accounting-section" class="hidden">
                <!-- إحصائيات مالية -->
                <div class="stats-container">
                    <div class="stat-card" id="stat-total-revenue" onclick="filterAccountingData(&#39;revenue&#39;)">
                        <div class="stat-number" id="total-revenue">20000.00</div>
                        <div class="stat-label">💰 إجمالي الإيرادات</div>
                    </div>
                    <div class="stat-card" id="stat-pending-payments" onclick="filterAccountingData(&#39;pending&#39;)">
                        <div class="stat-number" id="pending-payments">0</div>
                        <div class="stat-label">⏳ مدفوعات معلقة</div>
                    </div>
                    <div class="stat-card" id="stat-paid-invoices" onclick="filterAccountingData(&#39;paid&#39;)">
                        <div class="stat-number" id="paid-invoices">11</div>
                        <div class="stat-label">✅ فواتير مدفوعة</div>
                    </div>
                    <div class="stat-card" id="stat-total-customers" onclick="filterAccountingData(&#39;customers&#39;)">
                        <div class="stat-number" id="total-customers">6</div>
                        <div class="stat-label">👥 إجمالي العملاء</div>
                    </div>
                </div>

                <!-- تبويبات النظام المحاسبي -->
                <div class="tabs" style="margin-bottom: 20px; display: flex; flex-wrap: wrap; gap: 5px;">
                    <div class="tab" onclick="switchAccountingTab(&#39;dashboard&#39;)">📊 لوحة التحكم</div>
                    <div class="tab" onclick="switchAccountingTab(&#39;customers&#39;)">👥 العملاء والموردين</div>
                    <div class="tab" onclick="switchAccountingTab(&#39;invoices&#39;)">📄 الفواتير</div>
                    <div class="tab" onclick="switchAccountingTab(&#39;receipts&#39;)">📥 سندات القبض</div>
                    <div class="tab" onclick="switchAccountingTab(&#39;payments&#39;)">📤 سندات الصرف</div>
                    <div class="tab" onclick="switchAccountingTab(&#39;statements&#39;)">📋 كشوف الحسابات</div>
                    <div class="tab" onclick="switchAccountingTab(&#39;reports&#39;)">📈 التقارير</div>
                </div>

                <!-- لوحة التحكم المحاسبية -->
                <div id="dashboard-content" class="accounting-content hidden">
                    <div class="form-section">
                        <h2 style="margin-bottom: 24px; color: #1e293b; text-align: center;">📊 لوحة التحكم المحاسبية</h2>
                        
                        <!-- إحصائيات سريعة -->
                        <div class="report-summary" style="margin-bottom: 30px;">
                            <div class="summary-item" style="background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);">
                                <div class="summary-value" id="dashboard-total-revenue" style="color: #059669;">20000.00</div>
                                <div class="summary-label">💰 إجمالي الإيرادات</div>
                            </div>
                            <div class="summary-item" style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);">
                                <div class="summary-value" id="dashboard-pending-amount" style="color: #d97706;">0.00</div>
                                <div class="summary-label">⏳ المبالغ المعلقة</div>
                            </div>
                            <div class="summary-item" style="background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);">
                                <div class="summary-value" id="dashboard-total-customers" style="color: #2563eb;">6</div>
                                <div class="summary-label">👥 إجمالي العملاء</div>
                            </div>
                            <div class="summary-item" style="background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);">
                                <div class="summary-value" id="dashboard-monthly-revenue" style="color: #7c3aed;">1999.00</div>
                                <div class="summary-label">📅 إيرادات الشهر</div>
                            </div>
                        </div>

                        <!-- أحدث العمليات -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div class="report-section">
                                <h3 style="color: #1e293b; margin-bottom: 15px;">📄 أحدث الفواتير</h3>
                                <div id="recent-invoices">
                    <div style="padding: 10px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between;">
                        <span>14 - بجاش بن بجاش</span>
                        <span style="color: #059669; font-weight: bold;">0.00 ر.س</span>
                    </div>
                
                    <div style="padding: 10px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between;">
                        <span>13 - بجاش بن بجاش</span>
                        <span style="color: #059669; font-weight: bold;">0.00 ر.س</span>
                    </div>
                
                    <div style="padding: 10px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between;">
                        <span>12 - بجاش بن بجاش</span>
                        <span style="color: #059669; font-weight: bold;">0.00 ر.س</span>
                    </div>
                
                    <div style="padding: 10px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between;">
                        <span>11 - بجاش بن بجاش</span>
                        <span style="color: #059669; font-weight: bold;">0.00 ر.س</span>
                    </div>
                
                    <div style="padding: 10px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between;">
                        <span>10 - شركة الأمل للتجارة</span>
                        <span style="color: #059669; font-weight: bold;">0.00 ر.س</span>
                    </div>
                </div>
                            </div>
                            <div class="report-section">
                                <h3 style="color: #1e293b; margin-bottom: 15px;">💳 أحدث المعاملات</h3>
                                <div id="recent-transactions">
                    <div style="padding: 10px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between;">
                        <span>REC-0002 - بجاش بن بجاش</span>
                        <span style="color: #059669; font-weight: bold;">
                            +1999.00 ر.س
                        </span>
                    </div>
                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- محتوى الفواتير -->
                <div id="invoices-content" class="accounting-content hidden">
                    <div class="form-section">
                        <h2 style="margin-bottom: 24px; color: #1e293b; text-align: center;">📄 إدارة الفواتير</h2>
                        
                        <!-- البحث والفلترة -->
                        <div class="search-container">
                            <input type="text" class="search-input" id="invoiceSearch" placeholder="البحث في الفواتير..." onkeyup="searchInvoices()">
                            <span class="search-icon">🔍</span>
                        </div>

                        <div class="form-actions" style="margin-bottom: 20px;">
                            <select class="form-input" id="invoiceStatusFilter" onchange="filterInvoices()" style="width: auto; margin-left: 10px;">
                                <option value="all">جميع الفواتير</option>
                                <option value="pending">معلقة</option>
                                <option value="paid">مدفوعة</option>
                                <option value="overdue">متأخرة</option>
                            </select>
                            <button class="btn btn-primary" onclick="generateInvoiceReport()">📊 تقرير الفواتير</button>
                        </div>

                        <div id="invoicesList">
                <div class="invoice-card paid">
                    <div class="invoice-header">
                        <div class="invoice-number">INV-2024-001</div>
                        <div class="invoice-status status-paid">
                            مدفوعة
                        </div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">شركة الأمل للتجارة</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العمل:</span>
                            <span class="invoice-detail-value">طباعة أكياس</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الإنشاء:</span>
                            <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الاستحقاق:</span>
                            <span class="invoice-detail-value">٢٣‏/٧‏/٢٠٢٥</span>
                        </div>
                    </div>
                    <div class="invoice-amount">5000.00 ر.س</div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="viewInvoiceDetails(1750635369054.4294)">
                            📄 عرض التفاصيل
                        </button>
                        
                        <button class="btn btn-danger" onclick="deleteInvoice(1750635369054.4294)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            
                <div class="invoice-card paid">
                    <div class="invoice-header">
                        <div class="invoice-number">INV-2024-002</div>
                        <div class="invoice-status status-paid">
                            مدفوعة
                        </div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">مؤسسة النور</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العمل:</span>
                            <span class="invoice-detail-value">طباعة بروشورات</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الإنشاء:</span>
                            <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الاستحقاق:</span>
                            <span class="invoice-detail-value">٢٣‏/٧‏/٢٠٢٥</span>
                        </div>
                    </div>
                    <div class="invoice-amount">10000.00 ر.س</div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="viewInvoiceDetails(1750635369055.0996)">
                            📄 عرض التفاصيل
                        </button>
                        
                        <button class="btn btn-danger" onclick="deleteInvoice(1750635369055.0996)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            
                <div class="invoice-card paid">
                    <div class="invoice-header">
                        <div class="invoice-number">43</div>
                        <div class="invoice-status status-paid">
                            مدفوعة
                        </div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">مؤسسة النور</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العمل:</span>
                            <span class="invoice-detail-value">فيبب</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الإنشاء:</span>
                            <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الاستحقاق:</span>
                            <span class="invoice-detail-value">٢٣‏/٧‏/٢٠٢٥</span>
                        </div>
                    </div>
                    <div class="invoice-amount">0.00 ر.س</div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="viewInvoiceDetails(1750635369055.653)">
                            📄 عرض التفاصيل
                        </button>
                        
                        <button class="btn btn-danger" onclick="deleteInvoice(1750635369055.653)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            
                <div class="invoice-card paid">
                    <div class="invoice-header">
                        <div class="invoice-number">INV-2024-003</div>
                        <div class="invoice-status status-paid">
                            مدفوعة
                        </div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">مكتب المحاماة</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العمل:</span>
                            <span class="invoice-detail-value">طباعة كروت شخصية</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الإنشاء:</span>
                            <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الاستحقاق:</span>
                            <span class="invoice-detail-value">٢٣‏/٧‏/٢٠٢٥</span>
                        </div>
                    </div>
                    <div class="invoice-amount">5000.00 ر.س</div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="viewInvoiceDetails(1750635369055.8215)">
                            📄 عرض التفاصيل
                        </button>
                        
                        <button class="btn btn-danger" onclick="deleteInvoice(1750635369055.8215)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            
                <div class="invoice-card paid">
                    <div class="invoice-header">
                        <div class="invoice-number">7</div>
                        <div class="invoice-status status-paid">
                            مدفوعة
                        </div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">طاهر</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العمل:</span>
                            <span class="invoice-detail-value">طباعة</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الإنشاء:</span>
                            <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الاستحقاق:</span>
                            <span class="invoice-detail-value">٢٣‏/٧‏/٢٠٢٥</span>
                        </div>
                    </div>
                    <div class="invoice-amount">0.00 ر.س</div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="viewInvoiceDetails(1750636497230.1025)">
                            📄 عرض التفاصيل
                        </button>
                        
                        <button class="btn btn-danger" onclick="deleteInvoice(1750636497230.1025)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            
                <div class="invoice-card paid">
                    <div class="invoice-header">
                        <div class="invoice-number">9</div>
                        <div class="invoice-status status-paid">
                            مدفوعة
                        </div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">شركة الأمل للتجارة</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العمل:</span>
                            <span class="invoice-detail-value">طباعة اكياس لون واحد باسم علاء انور</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الإنشاء:</span>
                            <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الاستحقاق:</span>
                            <span class="invoice-detail-value">٢٣‏/٧‏/٢٠٢٥</span>
                        </div>
                    </div>
                    <div class="invoice-amount">0.00 ر.س</div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="viewInvoiceDetails(1750638957485.3713)">
                            📄 عرض التفاصيل
                        </button>
                        
                        <button class="btn btn-danger" onclick="deleteInvoice(1750638957485.3713)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            
                <div class="invoice-card paid">
                    <div class="invoice-header">
                        <div class="invoice-number">10</div>
                        <div class="invoice-status status-paid">
                            مدفوعة
                        </div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">شركة الأمل للتجارة</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العمل:</span>
                            <span class="invoice-detail-value">طباعة اكياس لون واحد وجهين باسم عوض بن عوض</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الإنشاء:</span>
                            <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الاستحقاق:</span>
                            <span class="invoice-detail-value">٢٣‏/٧‏/٢٠٢٥</span>
                        </div>
                    </div>
                    <div class="invoice-amount">0.00 ر.س</div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="viewInvoiceDetails(1750639034029.119)">
                            📄 عرض التفاصيل
                        </button>
                        
                        <button class="btn btn-danger" onclick="deleteInvoice(1750639034029.119)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            
                <div class="invoice-card paid">
                    <div class="invoice-header">
                        <div class="invoice-number">11</div>
                        <div class="invoice-status status-paid">
                            مدفوعة
                        </div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">بجاش بن بجاش</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العمل:</span>
                            <span class="invoice-detail-value">طباعة اكياس باسم محمد المحمدي</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الإنشاء:</span>
                            <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الاستحقاق:</span>
                            <span class="invoice-detail-value">٢٣‏/٧‏/٢٠٢٥</span>
                        </div>
                    </div>
                    <div class="invoice-amount">0.00 ر.س</div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="viewInvoiceDetails(1750639437453.8135)">
                            📄 عرض التفاصيل
                        </button>
                        
                        <button class="btn btn-danger" onclick="deleteInvoice(1750639437453.8135)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            
                <div class="invoice-card paid">
                    <div class="invoice-header">
                        <div class="invoice-number">12</div>
                        <div class="invoice-status status-paid">
                            مدفوعة
                        </div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">بجاش بن بجاش</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العمل:</span>
                            <span class="invoice-detail-value">طباعة اكياس باسم محمد المحمدي</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الإنشاء:</span>
                            <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الاستحقاق:</span>
                            <span class="invoice-detail-value">٢٣‏/٧‏/٢٠٢٥</span>
                        </div>
                    </div>
                    <div class="invoice-amount">0.00 ر.س</div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="viewInvoiceDetails(1750639788869.6155)">
                            📄 عرض التفاصيل
                        </button>
                        
                        <button class="btn btn-danger" onclick="deleteInvoice(1750639788869.6155)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            
                <div class="invoice-card paid">
                    <div class="invoice-header">
                        <div class="invoice-number">13</div>
                        <div class="invoice-status status-paid">
                            مدفوعة
                        </div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">بجاش بن بجاش</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العمل:</span>
                            <span class="invoice-detail-value">طباعة اكياس باسم محمد المحمدي العدد</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الإنشاء:</span>
                            <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الاستحقاق:</span>
                            <span class="invoice-detail-value">٢٣‏/٧‏/٢٠٢٥</span>
                        </div>
                    </div>
                    <div class="invoice-amount">0.00 ر.س</div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="viewInvoiceDetails(1750643263444.5522)">
                            📄 عرض التفاصيل
                        </button>
                        
                        <button class="btn btn-danger" onclick="deleteInvoice(1750643263444.5522)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            
                <div class="invoice-card paid">
                    <div class="invoice-header">
                        <div class="invoice-number">14</div>
                        <div class="invoice-status status-paid">
                            مدفوعة
                        </div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">بجاش بن بجاش</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العمل:</span>
                            <span class="invoice-detail-value">طباعة اكياس باسم محمد المحمدي</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الإنشاء:</span>
                            <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الاستحقاق:</span>
                            <span class="invoice-detail-value">٢٣‏/٧‏/٢٠٢٥</span>
                        </div>
                    </div>
                    <div class="invoice-amount">0.00 ر.س</div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="viewInvoiceDetails(*************.3145)">
                            📄 عرض التفاصيل
                        </button>
                        
                        <button class="btn btn-danger" onclick="deleteInvoice(*************.3145)">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            </div>
                    </div>
                </div>

                <!-- محتوى العملاء والموردين -->
                <div id="customers-content" class="accounting-content hidden">
                    <div class="form-section">
                        <h2 style="margin-bottom: 24px; color: #1e293b; text-align: center;">👥 إدارة العملاء والموردين</h2>
                        
                        <!-- نموذج إضافة عميل/مورد جديد -->
                        <div class="report-section" style="margin-bottom: 30px;">
                            <h3 style="color: #1e293b; margin-bottom: 20px;">➕ إضافة عميل أو مورد جديد</h3>
                            <form id="customerForm" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                                <div class="form-group">
                                    <label class="form-label">نوع الحساب</label>
                                    <select class="form-input" id="customerType" required="">
                                        <option value="">اختر النوع...</option>
                                        <option value="customer">عميل</option>
                                        <option value="supplier">مورد</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">اسم العميل/المورد</label>
                                    <input type="text" class="form-input" id="customerName" placeholder="اسم العميل أو المورد" required="">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-input" id="customerPhone" placeholder="رقم الهاتف">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-input" id="customerEmail" placeholder="البريد الإلكتروني">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">العنوان</label>
                                    <input type="text" class="form-input" id="customerAddress" placeholder="العنوان">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">الرصيد الافتتاحي</label>
                                    <input type="number" class="form-input" id="customerBalance" placeholder="0.00" step="0.01">
                                </div>
                                <div class="form-group" style="display: flex; align-items: end;">
                                    <button type="submit" class="btn btn-success" style="width: 100%;">
                                        ✅ إضافة العميل/المورد
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- فلترة العملاء -->
                        <div class="form-actions" style="margin-bottom: 20px;">
                            <select class="form-input" id="customerTypeFilter" onchange="filterCustomers()" style="width: auto; margin-left: 10px;">
                                <option value="all">جميع الحسابات</option>
                                <option value="customer">العملاء فقط</option>
                                <option value="supplier">الموردين فقط</option>
                            </select>
                            <input type="text" class="search-input" id="customerSearch" placeholder="البحث في العملاء والموردين..." onkeyup="searchCustomers()" style="flex: 1; margin-left: 10px;">
                        </div>
                        
                        <div id="customersList">
                    <div class="customer-card">
                        <div class="customer-header">
                            <div class="customer-name">
                                شركة الأمل للتجارة 
                                <span style="background: #fef3c7; 
                                             color: #d97706; 
                                             padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">
                                    مورد
                                </span>
                            </div>
                            <div class="customer-balance balance-zero">
                                0.00 ر.س
                            </div>
                        </div>
                        <div class="invoice-details">
                            
                            
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">إجمالي المعاملات:</span>
                                <span class="invoice-detail-value">0</span>
                            </div>
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">تاريخ التسجيل:</span>
                                <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                            </div>
                        </div>
                        <div class="job-actions">
                            <button class="btn btn-primary" onclick="generateStatement(&#39;شركة الأمل للتجارة&#39;)">
                                📋 كشف الحساب
                            </button>
                            <button class="btn btn-success" onclick="editCustomer(1750635369055.5388)">
                                ✏️ تعديل
                            </button>
                            <button class="btn btn-danger" onclick="deleteCustomer(1750635369055.5388)">
                                🗑️ حذف
                            </button>
                        </div>
                    </div>
                
                    <div class="customer-card">
                        <div class="customer-header">
                            <div class="customer-name">
                                مؤسسة النور 
                                <span style="background: #fef3c7; 
                                             color: #d97706; 
                                             padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">
                                    مورد
                                </span>
                            </div>
                            <div class="customer-balance balance-zero">
                                0.00 ر.س
                            </div>
                        </div>
                        <div class="invoice-details">
                            
                            
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">إجمالي المعاملات:</span>
                                <span class="invoice-detail-value">0</span>
                            </div>
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">تاريخ التسجيل:</span>
                                <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                            </div>
                        </div>
                        <div class="job-actions">
                            <button class="btn btn-primary" onclick="generateStatement(&#39;مؤسسة النور&#39;)">
                                📋 كشف الحساب
                            </button>
                            <button class="btn btn-success" onclick="editCustomer(1750635369055.3506)">
                                ✏️ تعديل
                            </button>
                            <button class="btn btn-danger" onclick="deleteCustomer(1750635369055.3506)">
                                🗑️ حذف
                            </button>
                        </div>
                    </div>
                
                    <div class="customer-card">
                        <div class="customer-header">
                            <div class="customer-name">
                                مكتب المحاماة 
                                <span style="background: #fef3c7; 
                                             color: #d97706; 
                                             padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">
                                    مورد
                                </span>
                            </div>
                            <div class="customer-balance balance-zero">
                                0.00 ر.س
                            </div>
                        </div>
                        <div class="invoice-details">
                            
                            
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">إجمالي المعاملات:</span>
                                <span class="invoice-detail-value">0</span>
                            </div>
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">تاريخ التسجيل:</span>
                                <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                            </div>
                        </div>
                        <div class="job-actions">
                            <button class="btn btn-primary" onclick="generateStatement(&#39;مكتب المحاماة&#39;)">
                                📋 كشف الحساب
                            </button>
                            <button class="btn btn-success" onclick="editCustomer(1750635369055.5984)">
                                ✏️ تعديل
                            </button>
                            <button class="btn btn-danger" onclick="deleteCustomer(1750635369055.5984)">
                                🗑️ حذف
                            </button>
                        </div>
                    </div>
                
                    <div class="customer-card">
                        <div class="customer-header">
                            <div class="customer-name">
                                طاهر 
                                <span style="background: #fef3c7; 
                                             color: #d97706; 
                                             padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">
                                    مورد
                                </span>
                            </div>
                            <div class="customer-balance balance-zero">
                                0.00 ر.س
                            </div>
                        </div>
                        <div class="invoice-details">
                            
                            
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">إجمالي المعاملات:</span>
                                <span class="invoice-detail-value">0</span>
                            </div>
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">تاريخ التسجيل:</span>
                                <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                            </div>
                        </div>
                        <div class="job-actions">
                            <button class="btn btn-primary" onclick="generateStatement(&#39;طاهر&#39;)">
                                📋 كشف الحساب
                            </button>
                            <button class="btn btn-success" onclick="editCustomer(1750635545111.747)">
                                ✏️ تعديل
                            </button>
                            <button class="btn btn-danger" onclick="deleteCustomer(1750635545111.747)">
                                🗑️ حذف
                            </button>
                        </div>
                    </div>
                
                    <div class="customer-card">
                        <div class="customer-header">
                            <div class="customer-name">
                                بجاش بن بجاش 
                                <span style="background: #dbeafe; 
                                             color: #1d4ed8; 
                                             padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">
                                    عميل
                                </span>
                            </div>
                            <div class="customer-balance balance-positive">
                                1999.00 ر.س
                            </div>
                        </div>
                        <div class="invoice-details">
                            
                                <div class="invoice-detail">
                                    <span class="invoice-detail-label">الهاتف:</span>
                                    <span class="invoice-detail-value">7777777</span>
                                </div>
                            
                            
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">إجمالي المعاملات:</span>
                                <span class="invoice-detail-value">1</span>
                            </div>
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">تاريخ التسجيل:</span>
                                <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                            </div>
                        </div>
                        <div class="job-actions">
                            <button class="btn btn-primary" onclick="generateStatement(&#39;بجاش بن بجاش&#39;)">
                                📋 كشف الحساب
                            </button>
                            <button class="btn btn-success" onclick="editCustomer(1750637763109)">
                                ✏️ تعديل
                            </button>
                            <button class="btn btn-danger" onclick="deleteCustomer(1750637763109)">
                                🗑️ حذف
                            </button>
                        </div>
                    </div>
                
                    <div class="customer-card">
                        <div class="customer-header">
                            <div class="customer-name">
                                هلالل 
                                <span style="background: #dbeafe; 
                                             color: #1d4ed8; 
                                             padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">
                                    عميل
                                </span>
                            </div>
                            <div class="customer-balance balance-zero">
                                0.00 ر.س
                            </div>
                        </div>
                        <div class="invoice-details">
                            
                                <div class="invoice-detail">
                                    <span class="invoice-detail-label">الهاتف:</span>
                                    <span class="invoice-detail-value">777784237</span>
                                </div>
                            
                            
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">إجمالي المعاملات:</span>
                                <span class="invoice-detail-value">0</span>
                            </div>
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">تاريخ التسجيل:</span>
                                <span class="invoice-detail-value">٢٣‏/٦‏/٢٠٢٥</span>
                            </div>
                        </div>
                        <div class="job-actions">
                            <button class="btn btn-primary" onclick="generateStatement(&#39;هلالل&#39;)">
                                📋 كشف الحساب
                            </button>
                            <button class="btn btn-success" onclick="editCustomer(*************)">
                                ✏️ تعديل
                            </button>
                            <button class="btn btn-danger" onclick="deleteCustomer(*************)">
                                🗑️ حذف
                            </button>
                        </div>
                    </div>
                </div>
                    </div>
                </div>

                <!-- محتوى سندات القبض -->
                <div id="receipts-content" class="accounting-content hidden">
                    <div class="form-section">
                        <h2 style="margin-bottom: 24px; color: #1e293b; text-align: center;">📥 سندات القبض</h2>
                        
                        <!-- نموذج إضافة سند قبض -->
                        <div class="report-section" style="margin-bottom: 30px;">
                            <h3 style="color: #1e293b; margin-bottom: 20px;">➕ إضافة سند قبض جديد</h3>
                            <form id="receiptForm" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                                <div class="form-group">
                                    <label class="form-label">رقم السند</label>
                                    <input type="text" class="form-input" id="receiptNumber" readonly="" style="background: #f0f9ff; font-weight: bold;">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">العميل</label>
                                    <select class="form-input" id="receiptCustomer" required=""><option value="">اختر...</option><option value="شركة الأمل للتجارة">شركة الأمل للتجارة (مورد)</option><option value="مؤسسة النور">مؤسسة النور (مورد)</option><option value="مكتب المحاماة">مكتب المحاماة (مورد)</option><option value="طاهر">طاهر (مورد)</option><option value="بجاش بن بجاش">بجاش بن بجاش (عميل)</option><option value="هلالل">هلالل (عميل)</option></select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">المبلغ المستلم</label>
                                    <input type="number" class="form-input" id="receiptAmount" placeholder="0.00" step="0.01" required="">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">طريقة الاستلام</label>
                                    <select class="form-input" id="receiptMethod" required="">
                                        <option value="">اختر الطريقة...</option>
                                        <option value="cash">نقداً</option>
                                        <option value="bank">تحويل بنكي</option>
                                        <option value="check">شيك</option>
                                        <option value="card">بطاقة ائتمان</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">البيان</label>
                                    <input type="text" class="form-input" id="receiptDescription" placeholder="وصف العملية">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">ملاحظات</label>
                                    <input type="text" class="form-input" id="receiptNotes" placeholder="ملاحظات إضافية">
                                </div>
                                <div class="form-group" style="display: flex; align-items: end;">
                                    <button type="submit" class="btn btn-success" style="width: 100%;">
                                        📥 إضافة سند القبض
                                    </button>
                                </div>
                            </form>
                        </div>

                        <div id="receiptsList">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- محتوى سندات الصرف -->
                <div id="payments-content" class="accounting-content hidden">
                    <div class="form-section">
                        <h2 style="margin-bottom: 24px; color: #1e293b; text-align: center;">📤 سندات الصرف</h2>
                        
                        <!-- نموذج إضافة سند صرف -->
                        <div class="report-section" style="margin-bottom: 30px;">
                            <h3 style="color: #1e293b; margin-bottom: 20px;">➕ إضافة سند صرف جديد</h3>
                            <form id="paymentForm" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                                <div class="form-group">
                                    <label class="form-label">رقم السند</label>
                                    <input type="text" class="form-input" id="paymentNumber" readonly="" style="background: #fef3c7; font-weight: bold;">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">المورد/المستفيد</label>
                                    <select class="form-input" id="paymentSupplier" required=""><option value="">اختر...</option><option value="شركة الأمل للتجارة">شركة الأمل للتجارة (مورد)</option><option value="مؤسسة النور">مؤسسة النور (مورد)</option><option value="مكتب المحاماة">مكتب المحاماة (مورد)</option><option value="طاهر">طاهر (مورد)</option><option value="بجاش بن بجاش">بجاش بن بجاش (عميل)</option><option value="هلالل">هلالل (عميل)</option></select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">المبلغ المدفوع</label>
                                    <input type="number" class="form-input" id="paymentAmountOut" placeholder="0.00" step="0.01" required="">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">طريقة الدفع</label>
                                    <select class="form-input" id="paymentMethodOut" required="">
                                        <option value="">اختر الطريقة...</option>
                                        <option value="cash">نقداً</option>
                                        <option value="bank">تحويل بنكي</option>
                                        <option value="check">شيك</option>
                                        <option value="card">بطاقة ائتمان</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">البيان</label>
                                    <input type="text" class="form-input" id="paymentDescription" placeholder="وصف العملية">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">ملاحظات</label>
                                    <input type="text" class="form-input" id="paymentNotesOut" placeholder="ملاحظات إضافية">
                                </div>
                                <div class="form-group" style="display: flex; align-items: end;">
                                    <button type="submit" class="btn btn-danger" style="width: 100%;">
                                        📤 إضافة سند الصرف
                                    </button>
                                </div>
                            </form>
                        </div>

                        <div id="paymentsList">
                    <div class="empty-state">
                        <div class="empty-state-icon">💳</div>
                        <h3>لا توجد مدفوعات</h3>
                        <p>استخدم النموذج أعلاه لتسجيل دفعة جديدة</p>
                    </div>
                </div>
                    </div>
                </div>

                <!-- محتوى كشوف الحسابات -->
                <div id="statements-content" class="accounting-content">
                    <div class="form-section">
                        <h2 style="margin-bottom: 24px; color: #1e293b; text-align: center;">📋 كشوف الحسابات</h2>
                        
                        <!-- اختيار العميل لكشف الحساب -->
                        <div class="report-section" style="margin-bottom: 30px;">
                            <h3 style="color: #1e293b; margin-bottom: 20px;">🔍 اختر العميل أو المورد</h3>
                            <div style="display: grid; grid-template-columns: 1fr auto auto; gap: 15px; align-items: end;">
                                <div class="form-group">
                                    <label class="form-label">العميل/المورد</label>
                                    <select class="form-input" id="statementCustomer" onchange="generateStatement()"><option value="">اختر...</option><option value="شركة الأمل للتجارة">شركة الأمل للتجارة (مورد)</option><option value="مؤسسة النور">مؤسسة النور (مورد)</option><option value="مكتب المحاماة">مكتب المحاماة (مورد)</option><option value="طاهر">طاهر (مورد)</option><option value="بجاش بن بجاش">بجاش بن بجاش (عميل)</option><option value="هلالل">هلالل (عميل)</option></select>
                                </div>
                                <button type="button" class="btn btn-primary" onclick="generateStatement()">
                                    📋 عرض كشف الحساب
                                </button>
                                <button type="button" class="btn btn-success" onclick="printStatement()">
                                    🖨️ طباعة الكشف
                                </button>
                                <button type="button" class="btn" onclick="sendStatementWhatsApp()" style="background: linear-gradient(135deg, #25d366, #128c7e); color: white;">
                                    📱 إرسال واتساب
                                </button>
                                <button type="button" class="btn" onclick="generateStatementPDF()" style="background: linear-gradient(135deg, #dc2626, #b91c1c); color: white;">
                                    📄 تحميل PDF
                                </button>
                            </div>
                        </div>

                        <div id="statementContent">
                <div class="report-section">
                    <div class="report-title">كشف حساب: شركة الأمل للتجارة</div>
                    <div style="text-align: center; margin-bottom: 20px; color: #64748b;">
                        من ٢٣‏/٦‏/٢٠٢٥ - نوع الحساب: مورد
                    </div>
                    
                    <table class="statement-table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>البيان</th>
                                <th>رقم المستند</th>
                                <th>مدين</th>
                                <th>دائن</th>
                                <th>الرصيد</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background: #f8fafc; font-weight: bold;">
                                <td colspan="5">الرصيد الافتتاحي</td>
                                <td>0.00</td>
                            </tr>
                            
                                    <tr>
                                        <td>٢٣‏/٦‏/٢٠٢٥</td>
                                        <td>فاتورة: طباعة أكياس <button class="btn-edit-small active" onclick="editInvoiceFromStatement(&#39;INV-2024-001&#39;)" title="تعديل الفاتورة">✏️</button></td>
                                        <td>INV-2024-001</td>
                                        <td>-</td>
                                        <td>٥٬٠٠٠ ريال يمني</td>
                                        <td>؜-٥٬٠٠٠ ريال يمني</td>
                                    </tr>
                                
                                    <tr>
                                        <td>٢٣‏/٦‏/٢٠٢٥</td>
                                        <td>فاتورة: طباعة اكياس لون واحد باسم علاء انور <button class="btn-edit-small" onclick="editInvoiceFromStatement(&#39;9&#39;)" title="تعديل الفاتورة">✏️</button></td>
                                        <td>9</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>؜-٥٬٠٠٠ ريال يمني</td>
                                    </tr>
                                
                                    <tr>
                                        <td>٢٣‏/٦‏/٢٠٢٥</td>
                                        <td>فاتورة: طباعة اكياس لون واحد وجهين باسم عوض بن عوض <button class="btn-edit-small active" onclick="editInvoiceFromStatement(&#39;10&#39;)" title="تعديل الفاتورة">✏️</button></td>
                                        <td>10</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>؜-٥٬٠٠٠ ريال يمني</td>
                                    </tr>
                                
                        </tbody>
                    </table>
                    
                    <div class="statement-balance negative">
                        الرصيد النهائي: ؜-٥٬٠٠٠ ريال يمني
                    </div>
                </div>
            </div>
                    </div>
                </div>

                <!-- محتوى المدفوعات -->
                <div id="payments-content" class="accounting-content hidden">
                    <div class="form-section">
                        <h2 style="margin-bottom: 24px; color: #1e293b; text-align: center;">💳 إدارة المدفوعات</h2>
                        
                        <!-- إضافة دفعة جديدة -->
                        <div class="form-section" style="background: #f0fdf4; border: 2px solid #10b981;">
                            <h3 style="color: #065f46; margin-bottom: 16px;">💰 تسجيل دفعة جديدة</h3>
                            <form id="paymentForm">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                    <div class="form-group">
                                        <label class="form-label">العميل</label>
                                        <select class="form-input" id="paymentCustomer" required="">
                                            <option value="">اختر العميل...</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">رقم الفاتورة</label>
                                        <select class="form-input" id="paymentInvoice" required="">
                                            <option value="">اختر الفاتورة...</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">المبلغ المدفوع</label>
                                        <input type="number" class="form-input" id="paymentAmount" placeholder="0.00" step="0.01" required="">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">طريقة الدفع</label>
                                        <select class="form-input" id="paymentMethod" required="">
                                            <option value="">اختر طريقة الدفع...</option>
                                            <option value="cash">نقداً</option>
                                            <option value="bank">تحويل بنكي</option>
                                            <option value="check">شيك</option>
                                            <option value="card">بطاقة ائتمان</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-input form-textarea" id="paymentNotes" placeholder="ملاحظات إضافية..."></textarea>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-success">💰 تسجيل الدفعة</button>
                                </div>
                            </form>
                        </div>

                        <div id="paymentsList">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- محتوى التقارير -->
                <div id="reports-content" class="accounting-content hidden">
                    <div class="form-section">
                        <h2 style="margin-bottom: 24px; color: #1e293b; text-align: center;">📊 التقارير المالية</h2>
                        
                        <!-- خيارات التقارير -->
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
                            <div class="stat-card" onclick="generateDailyReport()" style="cursor: pointer;">
                                <div class="stat-number">📅</div>
                                <div class="stat-label">تقرير يومي</div>
                            </div>
                            <div class="stat-card" onclick="generateMonthlyReport()" style="cursor: pointer;">
                                <div class="stat-number">📆</div>
                                <div class="stat-label">تقرير شهري</div>
                            </div>
                            <div class="stat-card" onclick="generateCustomerReport()" style="cursor: pointer;">
                                <div class="stat-number">👥</div>
                                <div class="stat-label">تقرير العملاء</div>
                            </div>
                            <div class="stat-card" onclick="generateProfitReport()" style="cursor: pointer;">
                                <div class="stat-number">💹</div>
                                <div class="stat-label">تقرير الأرباح</div>
                            </div>
                        </div>

                        <div id="reportContent">
                            <div class="empty-state">
                                <div class="empty-state-icon">📊</div>
                                <h3>اختر نوع التقرير</h3>
                                <p>انقر على أحد الخيارات أعلاه لعرض التقرير المطلوب</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم الإعدادات -->
            <div id="settings-section" class="hidden">
                <h2 style="text-align: center; color: #1e293b; margin-bottom: 30px;">⚙️ إعدادات النظام</h2>

                <!-- إعدادات الشركة -->
                <div class="form-section" style="margin-bottom: 30px;">
                    <h3 style="color: #1e293b; margin-bottom: 20px; border-bottom: 2px solid #3b82f6; padding-bottom: 10px;">🏢 معلومات الشركة</h3>
                    <form id="companySettingsForm" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label class="form-label">اسم الشركة/المطبعة</label>
                            <input type="text" class="form-input" id="companyName" placeholder="مطبعة الحديثة" value="مطبعة الحديثة">
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-input" id="companyPhone" placeholder="*********" value="*********">
                        </div>
                        <div class="form-group">
                            <label class="form-label">العنوان</label>
                            <input type="text" class="form-input" id="companyAddress" placeholder="صنعاء - اليمن" value="صنعاء - اليمن">
                        </div>
                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-input" id="companyEmail" placeholder="<EMAIL>" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label class="form-label">الموقع الإلكتروني</label>
                            <input type="text" class="form-input" id="companyWebsite" placeholder="www.modernprint.ye" value="www.modernprint.ye">
                        </div>
                        <div class="form-group">
                            <label class="form-label">شعار الشركة (رمز تعبيري)</label>
                            <input type="text" class="form-input" id="companyLogo" placeholder="🖨️" value="🖨️" maxlength="2">
                        </div>
                    </form>

                    <div style="text-align: center; margin-top: 20px;">
                        <button type="button" onclick="saveCompanySettings()" class="btn btn-success" style="padding: 12px 30px;">
                            💾 حفظ إعدادات الشركة
                        </button>
                    </div>
                </div>

                <!-- إعدادات تصميم الفاتورة -->
                <div class="form-section" style="margin-bottom: 30px;">
                    <h3 style="color: #1e293b; margin-bottom: 20px; border-bottom: 2px solid #8b5cf6; padding-bottom: 10px;">🎨 تصميم الفاتورة</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label class="form-label">نمط التصميم</label>
                            <select class="form-input" id="invoiceTheme" onchange="updateInvoicePreview()">
                                <option value="modern">حديث</option>
                                <option value="classic">كلاسيكي</option>
                                <option value="elegant">أنيق</option>
                                <option value="minimal">بسيط</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">اللون الأساسي</label>
                            <input type="color" class="form-input" id="invoiceColor" onchange="updateInvoicePreview()" value="#3b82f6">
                        </div>
                        <div class="form-group">
                            <label class="form-label">نمط الرأس</label>
                            <select class="form-input" id="invoiceHeaderStyle" onchange="updateInvoicePreview()">
                                <option value="gradient">متدرج</option>
                                <option value="solid">لون واحد</option>
                                <option value="bordered">مع حدود</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">رسالة التذييل</label>
                            <input type="text" class="form-input" id="footerMessage" placeholder="🙏 شكراً لثقتكم بنا" onchange="updateInvoicePreview()">
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <h4 style="color: #1e293b; margin-bottom: 15px;">خيارات العرض</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="showLogo" onchange="updateInvoicePreview()" checked="">
                                <span>عرض الشعار</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="showBorder" onchange="updateInvoicePreview()" checked="">
                                <span>عرض الحدود</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="showFooterMessage" onchange="updateInvoicePreview()" checked="">
                                <span>عرض رسالة التذييل</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="showCompanyDetails" onchange="updateInvoicePreview()" checked="">
                                <span>عرض تفاصيل الشركة</span>
                            </label>
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <h4 style="color: #1e293b; margin-bottom: 15px;">📱 إعدادات رسالة الواتساب</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px;">
                            <div class="form-group">
                                <label class="form-label">نمط رسالة الواتساب</label>
                                <select class="form-input" id="whatsappStyle" onchange="updateWhatsAppPreview()">
                                    <option value="modern">حديث مع رموز</option>
                                    <option value="classic">كلاسيكي بسيط</option>
                                    <option value="business">رسمي للأعمال</option>
                                    <option value="elegant">أنيق مع حدود</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">عنوان الرسالة</label>
                                <input type="text" class="form-input" id="whatsappHeader" placeholder="🖨️ *فاتورة طباعة*" onchange="updateWhatsAppPreview()">
                            </div>
                            <div class="form-group">
                                <label class="form-label">تذييل الرسالة</label>
                                <input type="text" class="form-input" id="whatsappFooter" placeholder="🙏 شكراً لثقتكم بنا" onchange="updateWhatsAppPreview()">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="showCompanyInfoInWhatsApp" onchange="updateWhatsAppPreview()" checked="">
                                <span>عرض معلومات الشركة</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="showBordersInWhatsApp" onchange="updateWhatsAppPreview()" checked="">
                                <span>عرض الحدود والفواصل</span>
                            </label>
                        </div>

                        <h5 style="color: #1e293b; margin-bottom: 15px;">إخفاء العناصر التالية:</h5>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="hidePrinterName" onchange="updateWhatsAppPreview()">
                                <span>إخفاء اسم الطباع</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="hideJobDetails" onchange="updateWhatsAppPreview()">
                                <span>إخفاء تفاصيل العمل</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="hideInvoiceNumber" onchange="updateWhatsAppPreview()">
                                <span>إخفاء رقم الفاتورة</span>
                            </label>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button type="button" onclick="saveInvoiceDesignSettings()" class="btn btn-success" style="padding: 12px 30px;">
                            💾 حفظ إعدادات التصميم
                        </button>
                        <button type="button" onclick="resetInvoiceDesign()" class="btn btn-secondary" style="padding: 12px 30px; margin-right: 10px;">
                            🔄 إعادة تعيين التصميم
                        </button>
                        <button type="button" onclick="previewWhatsAppMessage()" class="btn btn-primary" style="padding: 12px 30px; margin-right: 10px;">
                            📱 معاينة رسالة الواتساب
                        </button>
                    </div>
                </div>

                <!-- معاينة الفاتورة -->
                <div class="form-section" style="margin-bottom: 30px;">
                    <h3 style="color: #1e293b; margin-bottom: 20px; border-bottom: 2px solid #10b981; padding-bottom: 10px;">👁️ معاينة الفاتورة</h3>
                    <div id="invoicePreview" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 20px; max-height: 400px; overflow-y: auto;">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>

                    <div style="text-align: center; margin-top: 15px;">
                        <button type="button" onclick="updateInvoicePreview()" class="btn btn-primary">
                            🔄 تحديث المعاينة
                        </button>
                    </div>
                </div>

                <!-- معاينة رسالة الواتساب -->
                <div class="form-section" style="margin-bottom: 30px;">
                    <h3 style="color: #1e293b; margin-bottom: 20px; border-bottom: 2px solid #22c55e; padding-bottom: 10px;">📱 معاينة رسالة الواتساب</h3>
                    <div id="whatsappPreview" style="background: #dcf8c6; border: 2px solid #25d366; border-radius: 18px; padding: 20px; font-family: &#39;Segoe UI&#39;, Tahoma, Geneva, Verdana, sans-serif; white-space: pre-line; direction: rtl; max-height: 400px; overflow-y: auto; box-shadow: 0 2px 10px rgba(37, 211, 102, 0.2);">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>

                    <div style="text-align: center; margin-top: 15px;">
                        <button type="button" onclick="updateWhatsAppPreview()" class="btn btn-success" style="padding: 8px 20px;">
                            🔄 تحديث معاينة الواتساب
                        </button>
                        <button type="button" onclick="testWhatsAppMessage()" class="btn btn-primary" style="padding: 8px 20px; margin-right: 10px;">
                            📱 اختبار الإرسال
                        </button>
                    </div>
                </div>

                <!-- إعدادات النظام -->
                <div class="form-section">
                    <h3 style="color: #1e293b; margin-bottom: 20px; border-bottom: 2px solid #f59e0b; padding-bottom: 10px;">🔧 إعدادات النظام</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label class="form-label">العملة الافتراضية</label>
                            <select class="form-input" id="defaultCurrency">
                                <option value="ريال يمني">ريال يمني</option>
                                <option value="ريال سعودي">ريال سعودي</option>
                                <option value="دولار أمريكي">دولار أمريكي</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">تنسيق التاريخ</label>
                            <select class="form-input" id="dateFormat">
                                <option value="en-US">إنجليزي (MM/DD/YYYY)</option>
                                <option value="ar-EG">عربي (DD/MM/YYYY)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">نمط الأرقام</label>
                            <select class="form-input" id="numberFormat">
                                <option value="english">إنجليزي (1, 2, 3)</option>
                                <option value="arabic">عربي (١, ٢, ٣)</option>
                            </select>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button type="button" onclick="saveSystemSettings()" class="btn btn-success" style="padding: 12px 30px;">
                            💾 حفظ إعدادات النظام
                        </button>
                        <button type="button" onclick="resetSettings()" class="btn btn-danger" style="padding: 12px 30px; margin-right: 10px;">
                            🔄 إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- مكتبة تحويل HTML إلى PDF -->
    <script src="./الجديد  نظام إدارة الطباعة_files/html2pdf.bundle.min.js.تنزيل"></script>

    <!-- مكتبة jsPDF لإنشاء ملفات PDF -->
    <script src="./الجديد  نظام إدارة الطباعة_files/jspdf.umd.min.js.تنزيل"></script>
    <script src="./الجديد  نظام إدارة الطباعة_files/html2canvas.min.js.تنزيل"></script>

    <!-- تحميل عميل API -->
    <script src="./الجديد  نظام إدارة الطباعة_files/api-client.js.تنزيل"></script>
    
    <script>
        // 🌐 حالة الاتصال بالخادم
        let isServerMode = false;
        let connectionStatus = 'offline';
        
        // متغيرات النظام
        let jobs = JSON.parse(localStorage.getItem('modernPrintingJobs')) || [];
        let printers = JSON.parse(localStorage.getItem('modernPrintingPrinters')) || [];
        let currentTab = 'jobs';
        let currentFilter = 'all';
        let jobCounter = parseInt(localStorage.getItem('modernPrintingJobCounter')) || 0;
        let currentJobImages = [];
        
        // تهيئة النظام مع دعم الخادم
        async function initializeSystem() {
            try {
                // محاولة الاتصال بالخادم
                if (window.printingAPI) {
                    const isConnected = await window.printingAPI.checkConnection();
                    if (isConnected) {
                        isServerMode = true;
                        connectionStatus = 'online';
                        console.log('🌐 تم الاتصال بالخادم - وضع الشبكة المحلية مفعل');
                        
                        // تحميل البيانات من الخادم
                        await loadDataFromServer();
                    } else {
                        console.log('📱 وضع محلي - البيانات محفوظة على الجهاز');
                    }
                }
            } catch (error) {
                console.log('📱 وضع محلي - البيانات محفوظة على الجهاز');
            }
            
            // إعداد مراقب حالة الاتصال
            if (window.printingAPI) {
                window.printingAPI.on('connectionChange', (isOnline) => {
                    connectionStatus = isOnline ? 'online' : 'offline';
                    updateConnectionStatus();
                });
            }
        }
        
        // تحميل البيانات من الخادم
        async function loadDataFromServer() {
            try {
                // تحميل الأعمال
                const jobsResponse = await window.printingAPI.getJobs();
                if (jobsResponse.success) {
                    jobs = jobsResponse.data.jobs || [];
                }
                
                // تحميل العملاء
                const customersResponse = await window.printingAPI.getCustomers();
                if (customersResponse.success) {
                    customers = customersResponse.data.customers || [];
                }
                
                console.log(`📊 تم تحميل ${jobs.length} عمل و ${customers.length} عميل من الخادم`);
                
            } catch (error) {
                console.error('خطأ في تحميل البيانات من الخادم:', error);
            }
        }
        
        // تحديث حالة الاتصال في الواجهة
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            if (statusElement) {
                if (connectionStatus === 'online') {
                    statusElement.innerHTML = '🌐 متصل بالخادم';
                    statusElement.className = 'connection-status online';
                } else {
                    statusElement.innerHTML = '📱 وضع محلي';
                    statusElement.className = 'connection-status offline';
                }
            }
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', async function() {
            // تهيئة النظام مع دعم الخادم
            await initializeSystem();
            
            // تهيئة الواجهة
            displayJobs();
            updateStats();
            displayPrinters();
            loadSamplePrinters();
            displayNextJobNumber();
            
            // تحديث حالة الاتصال
            updateConnectionStatus();

            // تفعيل الفلتر الافتراضي
            filterByStatus('all');

            // ربط الأحداث
            document.getElementById('jobForm').addEventListener('submit', addJob);
            document.getElementById('searchInput').addEventListener('input', filterJobs);
            document.getElementById('printerForm').addEventListener('submit', addPrinter);

            showMessage('🖨️ نظام إدارة الطباعة جاهز للاستخدام', 'success');
        });

        // تبديل التبويبات
        function switchTab(tabName) {
            // تحديث التبويبات
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // إخفاء جميع الأقسام
            document.querySelectorAll('[id$="-section"]').forEach(section => {
                section.classList.add('hidden');
            });

            // إظهار القسم المحدد
            document.getElementById(tabName + '-section').classList.remove('hidden');
            currentTab = tabName;

            // تغيير خلفية المحتوى للنظام المحاسبي
            const contentElement = document.querySelector('.content');
            if (tabName === 'accounting') {
                contentElement.classList.add('accounting-active');
            } else {
                contentElement.classList.remove('accounting-active');
            }

            // عرض رقم العمل التالي عند فتح تبويب إضافة العمل
            if (tabName === 'add-job') {
                displayNextJobNumber();
            }

            // تحديث النظام المحاسبي عند فتحه
            if (tabName === 'accounting') {
                updateAccountingData();
                updateDashboard();
                displayInvoices();
                displayCustomers();
                displayPayments();
                updateAccountingStats();
                updatePaymentFormOptions();
                updateCustomerFormOptions();
            }

            // تحميل الإعدادات عند فتح تبويب الإعدادات
            if (tabName === 'settings') {
                loadSettingsToForm();
                updateInvoicePreview();
                updateWhatsAppPreview();
            }
        }

        // توليد رقم العمل التلقائي
        function generateJobNumber() {
            jobCounter++;
            localStorage.setItem('modernPrintingJobCounter', jobCounter);

            return jobCounter.toString();
        }

        // عرض رقم العمل التالي في النموذج
        function displayNextJobNumber() {
            const nextNumber = (jobCounter + 1).toString();
            const nextJobNumberDiv = document.getElementById('nextJobNumber');
            const invoiceNumberInput = document.getElementById('invoiceNumber');
            
            if (nextJobNumberDiv) {
                nextJobNumberDiv.textContent = nextNumber;
            }
            
            // تحديث رقم الفاتورة ليكون مطابقاً لرقم العمل
            if (invoiceNumberInput) {
                invoiceNumberInput.value = nextNumber;
                invoiceNumberInput.placeholder = `رقم الفاتورة: ${nextNumber}`;
            }
        }

        // إضافة عمل جديد
        function addJob(event) {
            event.preventDefault();

            const mainJob = {
                id: Date.now(),
                jobNumber: generateJobNumber(),
                name: document.getElementById('jobName').value.trim(),
                invoiceNumber: document.getElementById('invoiceNumber').value.trim(),
                customerName: '',
                printerName: document.getElementById('printerNameSelect').value.trim() || 'غير محدد',
                totalQuantity: document.getElementById('totalQuantity').value.trim() || 'غير محدد',
                printedQuantity: 0,
                colorCount: '',
                details: document.getElementById('jobDetails').value.trim(),
                isUrgent: document.getElementById('isUrgent').checked,
                images: [...currentJobImages],
                status: 'pending',
                createdAt: new Date().toISOString()
            };

            // تحديث الحالة تلقائياً حسب البيانات المدخلة
            updateJobStatus(mainJob);

            if (!mainJob.name) {
                showMessage('❌ يرجى إدخال اسم العمل', 'error');
                return;
            }

            // إضافة العمل الرئيسي
            jobs.push(mainJob);

            // إضافة الأعمال الإضافية إذا وجدت
            let addedJobsCount = 1;
            if (additionalJobs.length > 0) {
                additionalJobs.forEach(additionalJob => {
                    if (additionalJob.name.trim()) {
                        const newJob = {
                            id: Date.now() + Math.random(),
                            jobNumber: generateJobNumber(),
                            name: additionalJob.name.trim(),
                            invoiceNumber: mainJob.invoiceNumber,
                            customerName: '',
                            printerName: mainJob.printerName,
                            totalQuantity: mainJob.totalQuantity,
                            printedQuantity: 0,
                            colorCount: '',
                            details: additionalJob.details.trim(),
                            isUrgent: mainJob.isUrgent,
                            images: [...currentJobImages],
                            status: 'pending',
                            createdAt: new Date().toISOString()
                        };

                        updateJobStatus(newJob);
                        jobs.push(newJob);
                        addedJobsCount++;
                    }
                });
            }

            saveData();
            displayJobs();
            updateStats();
            clearForm();
            displayNextJobNumber();

            // إعادة تعيين الأعمال الإضافية
            additionalJobs = [];
            document.getElementById('additionalJobsContainer').style.display = 'none';

            showMessage(`✅ تم إضافة ${addedJobsCount} عمل بنجاح!`, 'success');
            switchTab('jobs');
        }

        // عرض الأعمال
        function displayJobs(filteredJobs = null) {
            const container = document.getElementById('jobsList');
            let jobsToShow = filteredJobs;

            // إذا لم يتم تمرير فلتر، طبق الفلتر الحالي
            if (!jobsToShow) {
                jobsToShow = jobs;
                if (currentFilter !== 'all') {
                    jobsToShow = jobsToShow.filter(job => job.status === currentFilter);
                }
            }

            // ترتيب الأعمال: المستعجلة أولاً
            jobsToShow = jobsToShow.sort((a, b) => {
                if (a.isUrgent && !b.isUrgent) return -1;
                if (!a.isUrgent && b.isUrgent) return 1;
                return new Date(b.createdAt) - new Date(a.createdAt);
            });

            if (jobsToShow.length === 0) {
                container.innerHTML = `
                    <div class="empty-state" style="grid-column: 1 / -1;">
                        <div class="empty-state-icon">📭</div>
                        <h3>لا توجد أعمال</h3>
                        <p>ابدأ بإضافة عمل طباعة جديد</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = jobsToShow.map(job => `
                <div class="job-card ${job.status} ${job.isUrgent ? 'urgent' : ''}">
                    <div class="job-header">
                        <div class="job-title">${job.name}</div>
                        <div class="job-status ${getStatusClass(job)}">
                            ${getStatusText(job)} ${job.isUrgent ? '⚡' : ''}
                        </div>
                    </div>

                    <div class="job-details">
                        ${job.invoiceNumber ? `
                        <div class="job-detail-row">
                            <span class="job-detail-label">رقم الفاتورة:</span>
                            <span class="job-detail-value" style="color: #3b82f6; font-weight: bold;">${job.invoiceNumber}</span>
                        </div>
                        ` : ''}
                        ${job.customerName ? `
                        <div class="job-detail-row">
                            <span class="job-detail-label">العميل:</span>
                            <span class="job-detail-value" style="color: #059669; font-weight: bold;">${job.customerName}</span>
                        </div>
                        ` : ''}
                        <div class="job-detail-row">
                            <span class="job-detail-label">الكمية الإجمالية:</span>
                            <span class="job-detail-value">${job.totalQuantity}</span>
                        </div>
                        ${job.details ? `
                        <div class="job-detail-row">
                            <span class="job-detail-label">تفاصيل العمل:</span>
                            <span class="job-detail-value">${job.details}</span>
                        </div>
                        ` : ''}
                        <div class="job-detail-row">
                            <span class="job-detail-label">التاريخ:</span>
                            <span class="job-detail-value">${new Date(job.createdAt).toLocaleDateString('ar-EG')}</span>
                        </div>
                        ${job.images && job.images.length > 0 ? `
                        <div class="job-detail-row">
                            <span class="job-detail-label">الصور:</span>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                ${job.images.slice(0, 3).map(image => `
                                    <img src="${image.data}" style="
                                        width: 50px;
                                        height: 50px;
                                        object-fit: cover;
                                        border-radius: 6px;
                                        cursor: pointer;
                                        border: 2px solid #e5e7eb;
                                    " onclick="viewImage('${image.data}')" title="${image.name}">
                                `).join('')}
                                ${job.images.length > 3 ? `
                                    <div style="
                                        width: 50px;
                                        height: 50px;
                                        background: #f3f4f6;
                                        border-radius: 6px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        font-size: 12px;
                                        font-weight: bold;
                                        color: #6b7280;
                                        border: 2px solid #e5e7eb;
                                    ">+${job.images.length - 3}</div>
                                ` : ''}
                            </div>
                        </div>
                        ` : ''}
                    </div>

                    <!-- حقول التحديث المباشر -->
                    <div style="
                        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                        border: 2px solid #cbd5e1;
                        border-radius: 12px;
                        padding: 16px;
                        margin: 16px 0;
                        ${job.status === 'completed' ? 'border-color: #10b981; background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);' : ''}
                    ">
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            margin-bottom: 12px;
                        ">
                            <h4 style="
                                color: #1e293b;
                                margin: 0;
                                font-size: 14px;
                                font-weight: 600;
                            ">${job.status === 'completed' ? '✅ العمل مرحل - قابل للتعديل' : '📝 تحديث سريع'}:</h4>
                            ${job.jobNumber ? `
                            <span style="
                                background: linear-gradient(135deg, #1e3a8a, #1e40af);
                                color: white;
                                padding: 8px 16px;
                                border-radius: 20px;
                                font-size: 36px;
                                font-weight: bold;
                                font-family: 'Courier New', monospace;
                                letter-spacing: 2px;
                                box-shadow: 0 4px 12px rgba(30, 58, 138, 0.4);
                                border: 2px solid #1e40af;
                                min-width: 50px;
                                text-align: center;
                            ">${job.jobNumber}</span>
                            ` : ''}
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                            <div>
                                <label style="
                                    display: block;
                                    margin-bottom: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: #64748b;
                                ">الكمية المطبوعة:</label>
                                <input type="text"
                                    id="quantity_${job.id}"
                                    value="${job.printedQuantity && job.printedQuantity !== '0' ? job.printedQuantity : ''}"
                                    placeholder="الكمية المطبوعة"
                                    style="
                                        width: 100%;
                                        padding: 8px 12px;
                                        border: 2px solid #e5e7eb;
                                        border-radius: 6px;
                                        font-size: 14px;
                                        transition: all 0.3s ease;
                                    "
                                    onchange="quickUpdateJob(${job.id}, 'quantity', this.value)"
                                    oninput="quickUpdateJob(${job.id}, 'quantity', this.value)">
                            </div>

                            <div>
                                <label style="
                                    display: block;
                                    margin-bottom: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: #64748b;
                                ">اسم الطباع:</label>
                                <select
                                    id="printer_${job.id}"
                                    onchange="quickUpdateJob(${job.id}, 'printer', this.value)"
                                    style="
                                        width: 100%;
                                        padding: 8px 12px;
                                        border: 2px solid #e5e7eb;
                                        border-radius: 6px;
                                        font-size: 14px;
                                        transition: all 0.3s ease;
                                        background: white;
                                    ">
                                    <option value="">اختر الطباع...</option>
                                    ${getPrintersOptions(job.printerName)}
                                </select>
                            </div>
                        </div>

                        <!-- حقول إضافية تظهر بعد تسجيل الكمية والطباع -->
                        <div id="additional_fields_${job.id}" style="
                            display: ${(job.printedQuantity && job.printedQuantity !== '0' && job.printerName && job.printerName !== 'غير محدد') ? 'block' : 'none'};
                        ">
                            <!-- حقل السعر - يظهر بعد تسجيل الكمية والطباع -->
                            <div id="price_section_${job.id}" style="display: ${(job.printedQuantity && job.printerName && job.printedQuantity.trim() !== '' && job.printerName.trim() !== '' && job.printerName !== 'غير محدد') ? 'block' : 'none'}; margin-bottom: 12px;">
                                <div>
                                    <label style="
                                        display: block;
                                        margin-bottom: 4px;
                                        font-size: 12px;
                                        font-weight: 600;
                                        color: #64748b;
                                    ">سعر الوحدة (ريال يمني):</label>
                                    <input type="number"
                                        id="price_${job.id}"
                                        value="${job.unitPrice || ''}"
                                        placeholder="سعر الوحدة"
                                        min="0"
                                        step="0.01"
                                        style="
                                            width: 100%;
                                            padding: 8px 12px;
                                            border: 2px solid #e5e7eb;
                                            border-radius: 6px;
                                            font-size: 14px;
                                            transition: all 0.3s ease;
                                        "
                                        onchange="quickUpdateJob(${job.id}, 'unitPrice', this.value)"
                                        oninput="updateTotalAmount(${job.id}); checkAndShowCustomerField(${job.id})">
                                </div>

                                <!-- عرض المبلغ الإجمالي -->
                                <div id="total_amount_${job.id}" style="
                                    margin-top: 8px;
                                    padding: 8px 12px;
                                    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
                                    border: 2px solid #10b981;
                                    border-radius: 6px;
                                    text-align: center;
                                    font-weight: bold;
                                    color: #065f46;
                                    display: ${(job.unitPrice && job.printedQuantity) ? 'block' : 'none'};
                                ">
                                    💰 المبلغ الإجمالي: ${formatCurrency((job.unitPrice || 0) * (job.printedQuantity || 0))}
                                </div>
                            </div>

                            <!-- حقل العميل - يظهر بعد تسجيل السعر -->
                            <div id="customer_section_${job.id}" style="display: ${(job.unitPrice && job.unitPrice > 0) ? 'block' : 'none'}; margin-bottom: 12px;">
                                <div>
                                    <label style="
                                        display: block;
                                        margin-bottom: 4px;
                                        font-size: 12px;
                                        font-weight: 600;
                                        color: #64748b;
                                    ">اسم العميل:</label>
                                    <div style="position: relative;">
                                        <input type="text"
                                            id="customer_${job.id}"
                                            value="${job.customerName || ''}"
                                            placeholder="اسم العميل"
                                            style="
                                                width: 100%;
                                                padding: 8px 12px;
                                                border: 2px solid #e5e7eb;
                                                border-radius: 6px;
                                                font-size: 14px;
                                                transition: all 0.3s ease;
                                            "
                                            onchange="quickUpdateJob(${job.id}, 'customer', this.value)"
                                            oninput="showCustomerSuggestions(${job.id}, this.value)"
                                            onfocus="showCustomerSuggestions(${job.id}, this.value)"
                                            onblur="setTimeout(() => hideCustomerSuggestions(${job.id}), 200)">
                                        <div id="customer_suggestions_${job.id}" class="customer-suggestions" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button onclick="saveQuickUpdate(${job.id})" style="
                                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 6px;
                                font-weight: 600;
                                font-size: 12px;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">💾 حفظ التحديث</button>
                        </div>
                    </div>

                    <div class="job-actions">
                        ${job.status !== 'completed' ? `
                        <button class="btn btn-success" onclick="completeJob(${job.id})">
                            📤 ترحيل
                        </button>
                        ` : `
                        <button class="btn btn-primary" onclick="enableEditMode(${job.id})" style="
                            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 8px;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            margin-left: 8px;
                        ">
                            ✏️ تعديل العمل
                        </button>
                        <div style="
                            background: linear-gradient(135deg, #10b981, #059669);
                            color: white;
                            padding: 8px 16px;
                            border-radius: 8px;
                            text-align: center;
                            font-weight: 600;
                            font-size: 14px;
                            display: inline-block;
                        ">
                            📤 مرحل
                        </div>
                        `}
                        ${job.status === 'completed' && job.invoiceNumber ? `
                        <button class="btn btn-secondary" onclick="editInvoiceFromJobsList('${job.invoiceNumber}')" style="margin-left: 8px;">
                            ✏️ تعديل الفاتورة
                        </button>
                        <button onclick="generateInvoicePDF('${job.invoiceNumber}')" style="
                            background: linear-gradient(135deg, #dc2626, #b91c1c);
                            color: white;
                            border: none;
                            padding: 6px 12px;
                            border-radius: 6px;
                            font-size: 12px;
                            cursor: pointer;
                            margin-left: 8px;
                        ">📄 تحميل PDF</button>
                        <button onclick="sendInvoicePDFWhatsApp('${job.invoiceNumber}')" style="
                            background: linear-gradient(135deg, #25d366, #128c7e);
                            color: white;
                            border: none;
                            padding: 6px 12px;
                            border-radius: 6px;
                            font-size: 12px;
                            cursor: pointer;
                            margin-left: 8px;
                        ">📱 PDF واتساب</button>
                        ` : ''}
                        ${job.status !== 'completed' ? `
                        <button class="btn btn-danger" onclick="deleteJob(${job.id})">
                            🗑️ حذف
                        </button>
                        ` : `
                        <button class="btn btn-danger" onclick="confirmDeleteCompleted(${job.id})" style="opacity: 0.7;">
                            🗑️ حذف نهائي
                        </button>
                        `}
                    </div>
                </div>
            `).join('');
        }

        // تحديث عمل
        function updateJob(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            // إنشاء نافذة تحديث مخصصة
            showUpdateDialog(job);
        }

        // عرض نافذة تحديث العمل
        function showUpdateDialog(job) {
            const dialogHTML = `
                <div id="updateDialog" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.7);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                ">
                    <div style="
                        background: white;
                        border-radius: 16px;
                        padding: 30px;
                        max-width: 500px;
                        width: 90%;
                        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                    ">
                        <h3 style="
                            color: #1e293b;
                            margin-bottom: 20px;
                            text-align: center;
                            font-size: 20px;
                        ">📝 تحديث تقدم العمل</h3>

                        <div style="
                            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
                            padding: 15px;
                            border-radius: 12px;
                            margin-bottom: 20px;
                            border: 2px solid #cbd5e1;
                        ">
                            <strong style="color: #3b82f6;">العمل:</strong> ${job.name}
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="
                                display: block;
                                margin-bottom: 8px;
                                font-weight: 600;
                                color: #374151;
                            ">الكمية المطبوعة:</label>
                            <input type="text" id="updateQuantity" value="${job.printedQuantity && job.printedQuantity !== '0' ? job.printedQuantity : ''}" style="
                                width: 100%;
                                padding: 12px 16px;
                                border: 2px solid #e5e7eb;
                                border-radius: 8px;
                                font-size: 16px;
                                transition: all 0.3s ease;
                            " placeholder="أدخل الكمية المطبوعة">
                        </div>

                        <div style="margin-bottom: 25px;">
                            <label style="
                                display: block;
                                margin-bottom: 8px;
                                font-weight: 600;
                                color: #374151;
                            ">اسم الطباع:</label>
                            <select id="updatePrinter" style="
                                width: 100%;
                                padding: 12px 16px;
                                border: 2px solid #e5e7eb;
                                border-radius: 8px;
                                font-size: 16px;
                                transition: all 0.3s ease;
                                background: white;
                            ">
                                <option value="">اختر الطباع...</option>
                                ${getPrintersOptions(job.printerName)}
                            </select>
                        </div>

                        <div style="
                            display: flex;
                            gap: 12px;
                            justify-content: center;
                        ">
                            <button onclick="saveJobUpdate(${job.id})" style="
                                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                font-weight: 600;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">✅ حفظ التحديث</button>

                            <button onclick="closeUpdateDialog()" style="
                                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                font-weight: 600;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">❌ إلغاء</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', dialogHTML);

            // تركيز على حقل الكمية
            document.getElementById('updateQuantity').focus();
            document.getElementById('updateQuantity').select();
        }

        // حفظ تحديث العمل
        function saveJobUpdate(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            const newQuantity = document.getElementById('updateQuantity').value.trim();
            const newPrinter = document.getElementById('updatePrinter').value.trim();

            if (!newQuantity) {
                alert('يرجى إدخال الكمية المطبوعة');
                return;
            }

            if (!newPrinter) {
                alert('يرجى إدخال اسم الطباع');
                return;
            }

            // تحديث بيانات العمل
            job.printedQuantity = newQuantity;
            job.printerName = newPrinter;

            if (job.printedQuantity && job.printedQuantity !== '0') {
                job.status = 'in-progress';
            }

            saveData();
            displayJobs();
            updateStats();
            closeUpdateDialog();
            showMessage('✅ تم تحديث العمل بنجاح', 'success');
        }

        // إغلاق نافذة التحديث
        function closeUpdateDialog() {
            const dialog = document.getElementById('updateDialog');
            if (dialog) {
                dialog.remove();
            }
        }

        // إضافة طباع جديد
        function addPrinter(event) {
            event.preventDefault();

            const printer = {
                id: Date.now(),
                name: document.getElementById('printerName').value.trim(),
                phone: document.getElementById('printerPhone').value.trim(),
                specialty: document.getElementById('printerSpecialty').value.trim(),
                createdAt: new Date().toISOString()
            };

            if (!printer.name) {
                showMessage('❌ يرجى إدخال اسم الطباع', 'error');
                return;
            }

            // التحقق من عدم تكرار الاسم
            if (printers.some(p => p.name === printer.name)) {
                showMessage('❌ هذا الطباع موجود بالفعل', 'error');
                return;
            }

            printers.push(printer);
            savePrintersData();
            displayPrinters();
            updatePrintersDropdown();
            clearPrinterForm();

            showMessage('✅ تم إضافة الطباع بنجاح!', 'success');
        }

        // عرض الطباعين
        function displayPrinters() {
            const container = document.getElementById('printersList');

            if (printers.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">👨‍💼</div>
                        <h3>لا يوجد طباعين</h3>
                        <p>ابدأ بإضافة طباع جديد</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = printers.map(printer => `
                <div class="job-card" style="margin-bottom: 15px;">
                    <div class="job-header">
                        <div class="job-title">👤 ${printer.name}</div>
                        <div class="job-status status-completed">نشط</div>
                    </div>

                    <div class="job-details">
                        ${printer.phone ? `
                        <div class="job-detail-row">
                            <span class="job-detail-label">الهاتف:</span>
                            <span class="job-detail-value">${printer.phone}</span>
                        </div>
                        ` : ''}
                        ${printer.specialty ? `
                        <div class="job-detail-row">
                            <span class="job-detail-label">التخصص:</span>
                            <span class="job-detail-value">${printer.specialty}</span>
                        </div>
                        ` : ''}
                        <div class="job-detail-row">
                            <span class="job-detail-label">تاريخ الإضافة:</span>
                            <span class="job-detail-value">${new Date(printer.createdAt).toLocaleDateString('ar-EG')}</span>
                        </div>
                    </div>

                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="editPrinter(${printer.id})">
                            📝 تعديل
                        </button>
                        <button class="btn btn-danger" onclick="deletePrinter(${printer.id})">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // حذف طباع
        function deletePrinter(printerId) {
            const printer = printers.find(p => p.id === printerId);
            if (!printer) return;

            if (!confirm(`هل أنت متأكد من حذف الطباع: "${printer.name}"؟`)) return;

            printers = printers.filter(p => p.id !== printerId);

            savePrintersData();
            displayPrinters();
            updatePrintersDropdown();
            showMessage('✅ تم حذف الطباع بنجاح', 'success');
        }

        // تعديل طباع
        function editPrinter(printerId) {
            const printer = printers.find(p => p.id === printerId);
            if (!printer) return;

            const newName = prompt('اسم الطباع:', printer.name);
            if (newName === null) return;

            if (!newName.trim()) {
                alert('يرجى إدخال اسم صحيح');
                return;
            }

            // التحقق من عدم تكرار الاسم
            if (printers.some(p => p.name === newName.trim() && p.id !== printerId)) {
                alert('هذا الاسم موجود بالفعل');
                return;
            }

            printer.name = newName.trim();

            savePrintersData();
            displayPrinters();
            updatePrintersDropdown();
            showMessage('✅ تم تعديل الطباع بنجاح', 'success');
        }

        // تحديث قائمة الطباعين المنسدلة
        function updatePrintersDropdown() {
            const select = document.getElementById('printerNameSelect');
            if (select) {
                select.innerHTML = '<option value="">اختر الطباع...</option>' +
                    printers.map(printer => `<option value="${printer.name}">${printer.name}</option>`).join('');
            }
        }

        // الحصول على خيارات الطباعين للتحديث
        function getPrintersOptions(selectedPrinter = '') {
            return printers.map(printer =>
                `<option value="${printer.name}" ${printer.name === selectedPrinter ? 'selected' : ''}>${printer.name}</option>`
            ).join('');
        }

        // حفظ بيانات الطباعين
        function savePrintersData() {
            localStorage.setItem('modernPrintingPrinters', JSON.stringify(printers));
        }

        // مسح نموذج الطباع
        function clearPrinterForm() {
            document.getElementById('printerForm').reset();
        }

        // تحديث سريع للعمل مع الذكاء
        function quickUpdateJob(jobId, field, value) {
            // تخزين التحديث مؤقتاً
            if (!window.tempUpdates) {
                window.tempUpdates = {};
            }
            if (!window.tempUpdates[jobId]) {
                window.tempUpdates[jobId] = {};
            }
            window.tempUpdates[jobId][field] = value;

            // التحقق من إظهار حقل السعر
            checkAndShowPriceField(jobId);
        }

        // 🧠 فحص وإظهار حقل السعر عند اكتمال الكمية والطباع
        function checkAndShowPriceField(jobId) {
            const quantityInput = document.getElementById(`quantity_${jobId}`);
            const printerSelect = document.getElementById(`printer_${jobId}`);
            const priceSection = document.getElementById(`price_section_${jobId}`);

            if (quantityInput && printerSelect && priceSection) {
                const hasQuantity = quantityInput.value && quantityInput.value.trim() !== '' && quantityInput.value.trim() !== '0';
                const hasPrinter = printerSelect.value && printerSelect.value.trim() !== '' && printerSelect.value !== 'غير محدد';

                if (hasQuantity && hasPrinter && priceSection.style.display === 'none') {
                    // إظهار حقل السعر مع تأثير جميل
                    priceSection.style.display = 'block';
                    priceSection.style.opacity = '0';
                    priceSection.style.transform = 'translateY(-10px)';
                    priceSection.style.transition = 'all 0.4s ease';

                    setTimeout(() => {
                        priceSection.style.opacity = '1';
                        priceSection.style.transform = 'translateY(0)';
                    }, 50);
                }
            }
        }

        // 🧠 فحص وإظهار حقل العميل عند إدخال السعر
        function checkAndShowCustomerField(jobId) {
            const priceInput = document.getElementById(`price_${jobId}`);
            const customerSection = document.getElementById(`customer_section_${jobId}`);

            if (priceInput && customerSection) {
                const hasPrice = priceInput.value && parseFloat(priceInput.value) > 0;

                if (hasPrice && customerSection.style.display === 'none') {
                    // إظهار حقل العميل مع تأثير جميل
                    customerSection.style.display = 'block';
                    customerSection.style.opacity = '0';
                    customerSection.style.transform = 'translateY(-10px)';
                    customerSection.style.transition = 'all 0.4s ease';

                    setTimeout(() => {
                        customerSection.style.opacity = '1';
                        customerSection.style.transform = 'translateY(0)';
                    }, 50);
                }
            }
        }

        // تحديث المبلغ الإجمالي
        function updateTotalAmount(jobId) {
            const priceInput = document.getElementById(`price_${jobId}`);
            const quantityInput = document.getElementById(`quantity_${jobId}`);
            const totalAmountDiv = document.getElementById(`total_amount_${jobId}`);

            if (priceInput && quantityInput && totalAmountDiv) {
                const price = parseFloat(priceInput.value) || 0;
                const quantity = parseFloat(quantityInput.value) || 0;
                const total = price * quantity;

                if (price > 0 && quantity > 0) {
                    totalAmountDiv.style.display = 'block';
                    totalAmountDiv.innerHTML = `💰 المبلغ الإجمالي: ${formatCurrency(total)}`;
                } else {
                    totalAmountDiv.style.display = 'none';
                }
            }
        }

        // إظهار/إخفاء الحقول الإضافية
        function toggleAdditionalFields(jobId) {
            const quantityInput = document.getElementById(`quantity_${jobId}`);
            const printerSelect = document.getElementById(`printer_${jobId}`);
            const additionalFields = document.getElementById(`additional_fields_${jobId}`);

            if (quantityInput && printerSelect && additionalFields) {
                const hasQuantity = quantityInput.value.trim() && quantityInput.value.trim() !== '0';
                const hasPrinter = printerSelect.value.trim() && printerSelect.value.trim() !== 'غير محدد';

                if (hasQuantity && hasPrinter) {
                    additionalFields.style.display = 'block';
                } else {
                    additionalFields.style.display = 'none';
                }
            }
        }

        // حفظ التحديث السريع
        function saveQuickUpdate(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            const quantityInput = document.getElementById(`quantity_${jobId}`);
            const printerSelect = document.getElementById(`printer_${jobId}`);
            const priceInput = document.getElementById(`price_${jobId}`);
            const customerInput = document.getElementById(`customer_${jobId}`);

            const newQuantity = quantityInput.value.trim();
            const newPrinter = printerSelect.value.trim();
            const newPrice = priceInput ? priceInput.value.trim() : '';
            const newCustomer = customerInput ? customerInput.value.trim() : '';

            if (!newQuantity) {
                showMessage('❌ يرجى إدخال الكمية المطبوعة', 'error');
                quantityInput.focus();
                return;
            }

            if (!newPrinter) {
                showMessage('❌ يرجى اختيار الطباع', 'error');
                printerSelect.focus();
                return;
            }

            // تحديث بيانات العمل
            job.printedQuantity = newQuantity;
            job.printerName = newPrinter;
            job.unitPrice = parseFloat(newPrice) || 0;
            job.customerName = newCustomer;

            // إنشاء رقم فاتورة تلقائي إذا لم يكن موجوداً
            if (!job.invoiceNumber && job.unitPrice > 0 && job.customerName) {
                job.invoiceNumber = `INV-${Date.now()}`;
            }

            // تحديث الحالة تلقائياً حسب البيانات المتوفرة
            updateJobStatus(job);

            saveData();
            displayJobs();
            updateStats();
            showMessage('✅ تم تحديث العمل بنجاح', 'success');

            // إظهار أزرار المشاركة والـ PDF إذا تم إدخال اسم العميل
            if (newCustomer && newCustomer.trim() !== '') {
                showShareButtons(jobId);
            }
        }

        // تحديث حالة العمل تلقائياً
        function updateJobStatus(job) {
            const hasPrinter = job.printerName && job.printerName !== 'غير محدد';
            const hasQuantity = job.printedQuantity && job.printedQuantity !== '0';
            const hasPrice = job.unitPrice && job.unitPrice > 0;
            const hasCustomer = job.customerName && job.customerName.trim() !== '';

            if (hasQuantity && hasPrinter && hasPrice && hasCustomer) {
                // إذا كانت جميع البيانات متوفرة → مكتملة
                const wasNotCompleted = job.status !== 'completed';
                job.status = 'completed';
                job.completedAt = new Date().toISOString();

                // إنشاء رقم فاتورة تلقائي إذا لم يكن موجوداً
                if (!job.invoiceNumber) {
                    job.invoiceNumber = `INV-${Date.now()}`;
                }

                // إرسال إشعار إذا كان العمل قد اكتمل للتو
                if (wasNotCompleted && job.customerName && job.invoiceNumber) {
                    setTimeout(() => {
                        notifyCustomerOnCompletion(job.id);
                    }, 1500);
                }
            } else if (hasQuantity && hasPrinter) {
                // إذا كانت الكمية والطباع متوفرة → تم الطباعة
                job.status = 'printed';
            } else if (hasPrinter) {
                // إذا تم تحديد الطباع فقط → قيد الطباعة
                job.status = 'in-progress';
            } else {
                // في الانتظار
                job.status = 'pending';
            }
        }

        // تحميل طباعين تجريبيين
        function loadSamplePrinters() {
            if (printers.length === 0) {
                printers = [
                    {
                        id: 1,
                        name: 'أحمد محمد',
                        phone: '01234567890',
                        specialty: 'طباعة ديجيتال',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 2,
                        name: 'محمد علي',
                        phone: '01098765432',
                        specialty: 'طباعة أوفست',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 3,
                        name: 'سعد أحمد',
                        phone: '01555666777',
                        specialty: 'طباعة كبيرة الحجم',
                        createdAt: new Date().toISOString()
                    }
                ];
                savePrintersData();
                updatePrintersDropdown();
            } else {
                updatePrintersDropdown();
            }
        }

        // ترحيل عمل
        function completeJob(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            if (!confirm(`هل أنت متأكد من ترحيل العمل: "${job.name}"؟`)) return;

            job.status = 'completed';
            job.completedAt = new Date().toISOString();

            saveData();
            displayJobs();
            updateStats();
            showMessage('📤 تم ترحيل العمل بنجاح! يمكنك الآن تعديل تفاصيله إذا لزم الأمر.', 'success');

            // إرسال إشعار للعميل إذا كان اسم العميل موجود
            if (job.customerName && job.invoiceNumber) {
                setTimeout(() => {
                    notifyCustomerOnCompletion(jobId);
                }, 1000);
            }
        }

        // تفعيل وضع التعديل للعمل المرحل
        function enableEditMode(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            // إنشاء نافذة تعديل
            const editModal = document.createElement('div');
            editModal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                direction: rtl;
            `;

            editModal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 16px;
                    padding: 30px;
                    max-width: 600px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                ">
                    <div style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 25px;
                        padding-bottom: 15px;
                        border-bottom: 2px solid #e2e8f0;
                    ">
                        <h2 style="
                            color: #1e293b;
                            margin: 0;
                            font-size: 24px;
                        ">✏️ تعديل العمل المرحل</h2>
                        <button onclick="closeEditModal()" style="
                            background: #ef4444;
                            color: white;
                            border: none;
                            border-radius: 50%;
                            width: 35px;
                            height: 35px;
                            cursor: pointer;
                            font-size: 18px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        ">×</button>
                    </div>

                    <form id="editJobForm" onsubmit="saveEditedJob(event, ${jobId})">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151;">اسم العمل:</label>
                                <input type="text" id="edit_jobName" value="${job.name}" style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                ">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151;">اسم العميل:</label>
                                <input type="text" id="edit_customerName" value="${job.customerName || ''}" style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                ">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151;">رقم الفاتورة:</label>
                                <input type="text" id="edit_invoiceNumber" value="${job.invoiceNumber || ''}" style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                ">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151;">الطباع:</label>
                                <select id="edit_printerName" style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                    background: white;
                                ">
                                    <option value="">اختر الطباع...</option>
                                    ${printers.map(printer => `
                                        <option value="${printer.name}" ${job.printerName === printer.name ? 'selected' : ''}>${printer.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151;">الكمية الإجمالية:</label>
                                <input type="text" id="edit_totalQuantity" value="${job.totalQuantity || ''}" style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                ">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151;">الكمية المطبوعة:</label>
                                <input type="text" id="edit_printedQuantity" value="${job.printedQuantity || ''}" style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                ">
                            </div>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151;">تفاصيل العمل:</label>
                            <textarea id="edit_details" rows="3" style="
                                width: 100%;
                                padding: 12px 16px;
                                border: 2px solid #e5e7eb;
                                border-radius: 8px;
                                font-size: 16px;
                                resize: vertical;
                            ">${job.details || ''}</textarea>
                        </div>

                        <div style="display: flex; gap: 15px; justify-content: center; margin-top: 30px;">
                            <button type="submit" style="
                                background: linear-gradient(135deg, #10b981, #059669);
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                font-weight: 600;
                                cursor: pointer;
                                font-size: 16px;
                            ">💾 حفظ التعديلات</button>
                            <button type="button" onclick="closeEditModal()" style="
                                background: linear-gradient(135deg, #6b7280, #4b5563);
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                font-weight: 600;
                                cursor: pointer;
                                font-size: 16px;
                            ">❌ إلغاء</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(editModal);
        }

        // حفظ التعديلات
        function saveEditedJob(event, jobId) {
            event.preventDefault();

            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            // تحديث بيانات العمل
            job.name = document.getElementById('edit_jobName').value;
            job.customerName = document.getElementById('edit_customerName').value;
            job.invoiceNumber = document.getElementById('edit_invoiceNumber').value;
            job.printerName = document.getElementById('edit_printerName').value;
            job.totalQuantity = document.getElementById('edit_totalQuantity').value;
            job.printedQuantity = document.getElementById('edit_printedQuantity').value;
            job.details = document.getElementById('edit_details').value;
            job.lastModified = new Date().toISOString();

            saveData();
            displayJobs();
            updateStats();
            closeEditModal();
            showMessage('✅ تم حفظ التعديلات بنجاح!', 'success');
        }

        // إغلاق نافذة التعديل
        function closeEditModal() {
            const modal = document.querySelector('div[style*="position: fixed"]');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // حذف عمل
        function deleteJob(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            if (!confirm(`هل أنت متأكد من حذف العمل: "${job.name}"؟`)) return;

            jobs = jobs.filter(j => j.id !== jobId);

            saveData();
            displayJobs();
            updateStats();
            showMessage('✅ تم حذف العمل بنجاح', 'success');
        }

        // تأكيد حذف عمل مكتمل
        function confirmDeleteCompleted(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            const confirmMessage = `⚠️ تحذير: هذا العمل مرحل ومؤرشف!\n\nالعمل: "${job.name}"\nالطباع: ${job.printerName}\nتاريخ الترحيل: ${new Date(job.completedAt).toLocaleDateString('ar-EG')}\n\nهل أنت متأكد من الحذف النهائي؟\nهذا الإجراء لا يمكن التراجع عنه!`;

            if (!confirm(confirmMessage)) return;

            // تأكيد إضافي
            if (!confirm('تأكيد نهائي: سيتم حذف العمل المرحل نهائياً!')) return;

            jobs = jobs.filter(j => j.id !== jobId);

            saveData();
            displayJobs();
            updateStats();
            showMessage('⚠️ تم حذف العمل المرحل نهائياً', 'success');
        }

        // تصفية الأعمال
        function filterJobs() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            let filteredJobs = jobs;

            // تطبيق فلتر البحث أولاً
            if (searchTerm) {
                filteredJobs = filteredJobs.filter(job =>
                    job.name.toLowerCase().includes(searchTerm) ||
                    job.printerName.toLowerCase().includes(searchTerm) ||
                    job.details.toLowerCase().includes(searchTerm) ||
                    (job.jobNumber && job.jobNumber.toLowerCase().includes(searchTerm)) ||
                    (job.invoiceNumber && job.invoiceNumber.toLowerCase().includes(searchTerm)) ||
                    (job.customerName && job.customerName.toLowerCase().includes(searchTerm))
                );

                // عند البحث، عرض جميع النتائج وتحديث الإحصائيات
                updateStatsWithSearch(filteredJobs);
                displayJobs(filteredJobs);

                // عرض رسالة البحث
                if (filteredJobs.length > 0) {
                    showMessage(`🔍 تم العثور على ${filteredJobs.length} نتيجة للبحث: "${searchTerm}"`, 'success');
                } else {
                    showMessage(`❌ لم يتم العثور على نتائج للبحث: "${searchTerm}"`, 'error');
                }
                return;
            }

            // تطبيق فلتر الحالة فقط عند عدم وجود بحث
            if (currentFilter !== 'all') {
                filteredJobs = filteredJobs.filter(job => job.status === currentFilter);
            }

            // إعادة تعيين الإحصائيات العادية عند عدم وجود بحث
            updateStats();
            displayJobs(filteredJobs);
        }

        // تحديث الإحصائيات مع نتائج البحث
        function updateStatsWithSearch(searchResults) {
            document.getElementById('totalJobs').textContent = searchResults.length;
            document.getElementById('pendingJobs').textContent = searchResults.filter(j => j.status === 'pending').length;
            document.getElementById('inProgressJobs').textContent = searchResults.filter(j => j.status === 'in-progress').length;
            document.getElementById('printedJobs').textContent = searchResults.filter(j => j.status === 'printed').length;
            document.getElementById('completedJobs').textContent = searchResults.filter(j => j.status === 'completed').length;

            // إزالة الفلتر النشط عند البحث
            document.querySelectorAll('.stat-card').forEach(card => {
                card.classList.remove('active');
            });

            // تفعيل مربع "إجمالي الأعمال" عند البحث
            const allCard = document.getElementById('stat-all');
            if (allCard) {
                allCard.classList.add('active');
            }
        }

        // فلترة حسب الحالة
        function filterByStatus(status) {
            currentFilter = status;

            // تحديث مظهر المربعات
            document.querySelectorAll('.stat-card').forEach(card => {
                card.classList.remove('active');
            });

            const activeCard = document.getElementById(`stat-${status}`);
            if (activeCard) {
                activeCard.classList.add('active');
            }

            // تطبيق الفلتر
            filterJobs();

            // عرض رسالة توضيحية
            const statusNames = {
                'all': 'جميع الأعمال',
                'pending': 'الأعمال في الانتظار',
                'in-progress': 'الأعمال قيد الطباعة',
                'printed': 'الأعمال المطبوعة',
                'completed': 'الأعمال المرحلة'
            };

            showMessage(`📊 عرض: ${statusNames[status]}`, 'success');
        }

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('totalJobs').textContent = jobs.length;
            document.getElementById('pendingJobs').textContent = jobs.filter(j => j.status === 'pending').length;
            document.getElementById('inProgressJobs').textContent = jobs.filter(j => j.status === 'in-progress').length;
            document.getElementById('printedJobs').textContent = jobs.filter(j => j.status === 'printed').length;
            document.getElementById('completedJobs').textContent = jobs.filter(j => j.status === 'completed').length;

            // تحديث الفلتر النشط
            if (currentFilter !== 'all') {
                const activeCard = document.getElementById(`stat-${currentFilter}`);
                if (activeCard) {
                    activeCard.classList.add('active');
                }
            }
        }

        // الحصول على فئة الحالة
        function getStatusClass(job) {
            if (job.status === 'completed') return 'status-completed';
            if (job.status === 'printed') return 'status-completed';
            if (job.status === 'in-progress') return 'status-progress';
            if (job.status === 'pending') return 'status-pending';
            if (job.isUrgent) return 'status-urgent';
            return 'status-pending';
        }

        // الحصول على نص الحالة
        function getStatusText(job) {
            const statusMap = {
                'pending': 'في الانتظار',
                'in-progress': 'قيد الطباعة',
                'printed': 'تم الطباعة',
                'completed': 'مكتمل'
            };

            let statusText = statusMap[job.status] || job.status;

            // إضافة علامة الاستعجال إذا كان العمل مستعجل
            if (job.isUrgent && job.status !== 'completed') {
                statusText += ' ⚡';
            }

            return statusText;
        }

        // حفظ البيانات في localStorage فقط
        function saveData() {
            // حفظ في localStorage
            localStorage.setItem('modernPrintingJobs', JSON.stringify(jobs));
            localStorage.setItem('modernPrintingPrinters', JSON.stringify(printers));
            localStorage.setItem('modernPrintingJobCounter', jobCounter);

            console.log('💾 تم الحفظ في المتصفح');
        }

        // حفظ تلقائي في ملف
        function autoSaveToFile() {
            try {
                const data = {
                    jobs: jobs,
                    printers: printers,
                    jobCounter: jobCounter,
                    lastSaved: new Date().toISOString(),
                    version: '1.0'
                };

                const dataStr = JSON.stringify(data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                // إنشاء رابط تحميل مخفي
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = 'printing-data.json';

                // إضافة الرابط للصفحة وتفعيله ثم حذفه
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // تنظيف الذاكرة
                URL.revokeObjectURL(link.href);

                console.log('💾 تم الحفظ التلقائي في ملف');
                showMessage('💾 تم الحفظ التلقائي', 'success');

            } catch (error) {
                console.error('خطأ في الحفظ التلقائي:', error);
                showMessage('❌ خطأ في الحفظ التلقائي', 'error');
            }
        }

        // تحميل البيانات من ملف
        function loadFromFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    if (data.jobs) jobs = data.jobs;
                    if (data.printers) printers = data.printers;
                    if (data.jobCounter) jobCounter = data.jobCounter;

                    // حفظ في localStorage أيضاً
                    localStorage.setItem('modernPrintingJobs', JSON.stringify(jobs));
                    localStorage.setItem('modernPrintingPrinters', JSON.stringify(printers));
                    localStorage.setItem('modernPrintingJobCounter', jobCounter);

                    renderJobs();
                    renderPrinters();
                    updatePrintersDropdown();
                    updateStats();

                    showMessage('✅ تم تحميل البيانات بنجاح!', 'success');
                } catch (error) {
                    showMessage('❌ خطأ في قراءة الملف!', 'error');
                }
            };
            reader.readAsText(file);
        }

        // مسح النموذج
        function clearForm() {
            document.getElementById('jobForm').reset();
            currentJobImages = [];
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('imagePreview').innerHTML = '';
            
            // تحديث رقم العمل ورقم الفاتورة للعمل التالي
            displayNextJobNumber();
        }

        // فتح الكاميرا
        function openCamera() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.capture = 'camera';
            input.onchange = handleImageUpload;
            input.click();
        }

        // معالجة رفع الصور
        function handleImageUpload(event) {
            const files = event.target.files;
            if (files.length === 0) return;

            for (let file of files) {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const imageData = {
                            name: file.name,
                            data: e.target.result,
                            size: file.size,
                            type: file.type
                        };
                        currentJobImages.push(imageData);
                        displayImagePreview();
                    };
                    reader.readAsDataURL(file);
                }
            }
        }

        // عرض معاينة الصور
        function displayImagePreview() {
            const previewDiv = document.getElementById('imagePreview');

            if (currentJobImages.length === 0) {
                previewDiv.style.display = 'none';
                return;
            }

            previewDiv.style.display = 'grid';
            previewDiv.innerHTML = currentJobImages.map((image, index) => `
                <div style="
                    position: relative;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                ">
                    <img src="${image.data}" style="
                        width: 100%;
                        height: 100px;
                        object-fit: cover;
                        cursor: pointer;
                    " onclick="viewImage('${image.data}')" title="${image.name}">
                    <button onclick="removeImage(${index})" style="
                        position: absolute;
                        top: 5px;
                        right: 5px;
                        background: #ef4444;
                        color: white;
                        border: none;
                        border-radius: 50%;
                        width: 25px;
                        height: 25px;
                        cursor: pointer;
                        font-size: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    ">×</button>
                </div>
            `).join('');
        }

        // حذف صورة
        function removeImage(index) {
            currentJobImages.splice(index, 1);
            displayImagePreview();
        }

        // عرض الصورة بحجم كامل
        function viewImage(imageSrc) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                cursor: pointer;
            `;

            const img = document.createElement('img');
            img.src = imageSrc;
            img.style.cssText = `
                max-width: 90%;
                max-height: 90%;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.5);
            `;

            modal.appendChild(img);
            modal.onclick = () => document.body.removeChild(modal);
            document.body.appendChild(modal);
        }

        // عرض رسالة
        function showMessage(text, type) {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = `<div class="message ${type}">${text}</div>`;

            setTimeout(() => {
                messagesDiv.innerHTML = '';
            }, 5000);
        }

        // إضافة بيانات تجريبية
        function addSampleData() {
            if (jobs.length === 0) {
                jobs = [
                    {
                        id: 1,
                        jobNumber: '1',
                        name: 'طباعة أكياس',
                        invoiceNumber: 'INV-2024-001',
                        customerName: 'شركة الأمل للتجارة',
                        printerName: 'أحمد محمد',
                        totalQuantity: '500',
                        printedQuantity: '0',
                        colorCount: '',
                        details: 'طباعة أكياس بلاستيكية - 4 ألوان - مقاس كبير',
                        isUrgent: true,
                        status: 'pending',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 2,
                        jobNumber: '2',
                        name: 'طباعة بروشورات',
                        invoiceNumber: 'INV-2024-002',
                        customerName: 'مؤسسة النور',
                        printerName: 'محمد علي',
                        totalQuantity: '1000',
                        printedQuantity: '0',
                        colorCount: '',
                        details: 'بروشورات إعلانية - لونين - ورق مطفي - مقاس A4',
                        isUrgent: true,
                        status: 'pending',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 3,
                        jobNumber: '3',
                        name: 'طباعة كروت شخصية',
                        invoiceNumber: 'INV-2024-003',
                        customerName: 'مكتب المحاماة',
                        printerName: 'سعد أحمد',
                        totalQuantity: '500',
                        printedQuantity: '0',
                        colorCount: '',
                        details: 'كروت شخصية للمحامين - لون واحد - ورق مقوى',
                        isUrgent: false,
                        status: 'pending',
                        createdAt: new Date().toISOString()
                    }
                ];

                // تحديث العداد ليبدأ من الرقم التالي
                jobCounter = 3;
                saveData();
                displayJobs();
                updateStats();
            }
        }

        // الحصول على قائمة العملاء المسجلين
        function getExistingCustomers() {
            const customers = new Set();
            jobs.forEach(job => {
                if (job.customerName && job.customerName.trim() !== '') {
                    customers.add(job.customerName.trim());
                }
            });
            return Array.from(customers).sort();
        }

        // عرض اقتراحات العملاء
        function showCustomerSuggestions(jobId, inputValue) {
            const suggestionsDiv = document.getElementById(`customer_suggestions_${jobId}`);
            if (!suggestionsDiv) return;

            // إذا كان الحقل فارغ، عرض جميع العملاء المسجلين
            if (!inputValue || inputValue.trim() === '') {
                const registeredCustomers = customers.filter(c => c.type === 'customer');
                
                if (registeredCustomers.length > 0) {
                    const suggestionsHTML = `
                        <div class="customer-suggestion-header">العملاء المسجلين في النظام:</div>
                        ${registeredCustomers.map(customer => `
                            <div class="customer-suggestion-item registered" onclick="selectCustomer(${jobId}, '${customer.name}')">
                                <div class="customer-info">
                                    <span class="customer-name">👤 ${customer.name}</span>
                                    ${customer.phone ? `<span class="customer-phone">📞 ${customer.phone}</span>` : ''}
                                </div>
                                <span class="customer-badge registered">مسجل في النظام</span>
                            </div>
                        `).join('')}
                        <div class="customer-suggestion-item add-new" onclick="showAddCustomerPrompt(${jobId})">
                            <span>➕ إضافة عميل جديد</span>
                        </div>
                    `;
                    suggestionsDiv.innerHTML = suggestionsHTML;
                    suggestionsDiv.style.display = 'block';
                } else {
                    suggestionsDiv.innerHTML = `
                        <div class="customer-suggestion-item add-new" onclick="showAddCustomerPrompt(${jobId})">
                            <span>➕ إضافة عميل جديد</span>
                        </div>
                    `;
                    suggestionsDiv.style.display = 'block';
                }
                return;
            }

            // البحث في العملاء المسجلين في النظام المحاسبي
            const registeredCustomers = customers.filter(customer =>
                customer.type === 'customer' && 
                customer.name.toLowerCase().includes(inputValue.toLowerCase())
            );

            // البحث في العملاء من الأعمال السابقة (غير المسجلين)
            const existingJobCustomers = getExistingCustomers().filter(customer =>
                customer.toLowerCase().includes(inputValue.toLowerCase()) &&
                !customers.find(c => c.name === customer)
            );

            const allSuggestions = [];
            
            // إضافة العملاء المسجلين أولاً
            registeredCustomers.forEach(customer => {
                allSuggestions.push({
                    name: customer.name,
                    phone: customer.phone,
                    type: 'registered'
                });
            });

            // إضافة العملاء من الأعمال السابقة
            existingJobCustomers.forEach(customerName => {
                allSuggestions.push({
                    name: customerName,
                    type: 'existing'
                });
            });

            if (allSuggestions.length === 0) {
                suggestionsDiv.innerHTML = `
                    <div class="customer-suggestion-item add-new" onclick="addNewCustomerFromInput(${jobId}, '${inputValue}')">
                        <span>➕ إضافة "${inputValue}" كعميل جديد</span>
                    </div>
                `;
                suggestionsDiv.style.display = 'block';
                return;
            }

            const suggestionsHTML = allSuggestions.map(customer => `
                <div class="customer-suggestion-item ${customer.type}" onclick="selectCustomer(${jobId}, '${customer.name}')">
                    <div class="customer-info">
                        <span class="customer-name">👤 ${customer.name}</span>
                        ${customer.phone ? `<span class="customer-phone">📞 ${customer.phone}</span>` : ''}
                    </div>
                    <span class="customer-badge ${customer.type}">
                        ${customer.type === 'registered' ? 'مسجل في النظام' : 'من الأعمال السابقة'}
                    </span>
                </div>
            `).join('') + `
                <div class="customer-suggestion-item add-new" onclick="addNewCustomerFromInput(${jobId}, '${inputValue}')">
                    <span>➕ إضافة عميل جديد</span>
                </div>
            `;

            suggestionsDiv.innerHTML = suggestionsHTML;
            suggestionsDiv.style.display = 'block';
        }

        // إخفاء اقتراحات العملاء
        function hideCustomerSuggestions(jobId) {
            const suggestionsDiv = document.getElementById(`customer_suggestions_${jobId}`);
            if (suggestionsDiv) {
                suggestionsDiv.style.display = 'none';
            }
        }

        // اختيار عميل من الاقتراحات
        function selectCustomer(jobId, customerName) {
            const input = document.getElementById(`customer_${jobId}`);
            if (input) {
                input.value = customerName;
                quickUpdateJob(jobId, 'customer', customerName);

                // التحقق من وجود العميل في النظام المحاسبي
                const existingCustomer = customers.find(c => c.name === customerName);
                
                if (existingCustomer) {
                    // إضافة تأثير بصري للتأكيد - عميل مسجل
                    input.style.background = 'linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%)';
                    input.style.borderColor = '#10b981';
                    showMessage(`✅ تم ربط العمل بالعميل المسجل: ${customerName}`, 'success');
                } else {
                    // إضافة العميل تلقائياً للنظام المحاسبي
                    const newCustomer = {
                        id: Date.now(),
                        name: customerName,
                        type: 'customer',
                        phone: '',
                        email: '',
                        address: '',
                        balance: 0,
                        totalJobs: 1,
                        totalAmount: 0,
                        createdAt: new Date().toISOString()
                    };
                    
                    customers.push(newCustomer);
                    saveAccountingData();
                    
                    // إضافة تأثير بصري للتأكيد - عميل جديد
                    input.style.background = 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)';
                    input.style.borderColor = '#f59e0b';
                    showMessage(`✅ تم إضافة العميل "${customerName}" للنظام المحاسبي وربطه بالعمل`, 'success');
                }

                setTimeout(() => {
                    input.style.background = '#f9fafb';
                    input.style.borderColor = '#e5e7eb';
                }, 2000);
            }
            hideCustomerSuggestions(jobId);
        }

        // إضافة عميل جديد من حقل الإدخال
        function addNewCustomerFromInput(jobId, customerName) {
            if (!customerName || customerName.trim() === '') {
                showAddCustomerPrompt(jobId);
                return;
            }

            const trimmedName = customerName.trim();
            
            // التحقق من عدم وجود العميل مسبقاً
            const existingCustomer = customers.find(c => c.name === trimmedName);
            if (existingCustomer) {
                selectCustomer(jobId, trimmedName);
                return;
            }

            // إضافة العميل الجديد للنظام المحاسبي
            const newCustomer = {
                id: Date.now(),
                name: trimmedName,
                type: 'customer',
                phone: '',
                email: '',
                address: '',
                balance: 0,
                totalJobs: 1,
                totalAmount: 0,
                createdAt: new Date().toISOString()
            };
            
            customers.push(newCustomer);
            saveAccountingData();
            
            // ربط العميل بالعمل
            const input = document.getElementById(`customer_${jobId}`);
            if (input) {
                input.value = trimmedName;
                quickUpdateJob(jobId, 'customer', trimmedName);
                
                // تأثير بصري للعميل الجديد
                input.style.background = 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)';
                input.style.borderColor = '#f59e0b';
                
                setTimeout(() => {
                    input.style.background = '#f9fafb';
                    input.style.borderColor = '#e5e7eb';
                }, 2000);
            }
            
            hideCustomerSuggestions(jobId);
            showMessage(`✅ تم إضافة العميل "${trimmedName}" للنظام المحاسبي وربطه بالعمل`, 'success');
            
            // إظهار خيارات المشاركة بعد حفظ العميل
            setTimeout(() => {
                showShareOptions(jobId, trimmedName);
            }, 1000);
        }

        // عرض نافذة إضافة عميل جديد
        function showAddCustomerPrompt(jobId) {
            const customerName = prompt('أدخل اسم العميل الجديد:');
            if (customerName && customerName.trim() !== '') {
                addNewCustomerFromInput(jobId, customerName.trim());
            }
        }

        // تحميل البيانات التجريبية عند بدء التشغيل
        setTimeout(() => {
            addSampleData();
        }, 1000);

        // ==================== دوال النظام المحاسبي ====================

        // متغيرات النظام المحاسبي
        let invoices = JSON.parse(localStorage.getItem('modernPrintingInvoices')) || [];
        let payments = JSON.parse(localStorage.getItem('modernPrintingPayments')) || [];
        let customers = JSON.parse(localStorage.getItem('modernPrintingCustomers')) || [];
        let receipts = JSON.parse(localStorage.getItem('modernPrintingReceipts')) || [];
        let paymentsOut = JSON.parse(localStorage.getItem('modernPrintingPaymentsOut')) || [];
        let currentAccountingTab = 'dashboard';
        let receiptCounter = parseInt(localStorage.getItem('modernPrintingReceiptCounter')) || 0;
        let paymentCounter = parseInt(localStorage.getItem('modernPrintingPaymentCounter')) || 0;

        // تبديل تبويبات النظام المحاسبي
        function switchAccountingTab(tabName) {
            // تحديث التبويبات
            document.querySelectorAll('#accounting-section .tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // إخفاء جميع المحتويات
            document.querySelectorAll('.accounting-content').forEach(content => {
                content.classList.add('hidden');
            });

            // إظهار المحتوى المحدد
            document.getElementById(tabName + '-content').classList.remove('hidden');
            currentAccountingTab = tabName;

            // تحديث البيانات حسب التبويب
            switch(tabName) {
                case 'dashboard':
                    updateDashboard();
                    break;
                case 'invoices':
                    displayInvoices();
                    break;
                case 'customers':
                    displayCustomers();
                    updateCustomerFormOptions();
                    break;
                case 'receipts':
                    displayReceipts();
                    updateReceiptFormOptions();
                    generateReceiptNumber();
                    break;
                case 'payments':
                    displayPaymentsOut();
                    updatePaymentFormOptions();
                    generatePaymentNumber();
                    break;
                case 'statements':
                    updateStatementFormOptions();
                    break;
                case 'reports':
                    updateReportFormOptions();
                    break;
            }
        }

        // تحديث بيانات النظام المحاسبي
        function updateAccountingData() {
            // تحديث الفواتير من الأعمال
            jobs.forEach(job => {
                if (job.invoiceNumber && job.customerName) {
                    const existingInvoice = invoices.find(inv => inv.invoiceNumber === job.invoiceNumber);
                    if (!existingInvoice) {
                        const invoice = {
                            id: Date.now() + Math.random(),
                            invoiceNumber: job.invoiceNumber,
                            customerName: job.customerName,
                            jobId: job.id,
                            jobName: job.name,
                            amount: calculateJobAmount(job),
                            status: job.status === 'completed' ? 'paid' : 'pending',
                            createdAt: job.createdAt,
                            dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 يوم من الآن
                        };
                        invoices.push(invoice);
                    }
                }
            });

            // تحديث العملاء
            const uniqueCustomers = [...new Set(jobs.map(job => job.customerName).filter(name => name))];
            uniqueCustomers.forEach(customerName => {
                const existingCustomer = customers.find(c => c.name === customerName);
                if (!existingCustomer) {
                    const customer = {
                        id: Date.now() + Math.random(),
                        name: customerName,
                        type: 'customer',
                        phone: '',
                        email: '',
                        address: '',
                        totalJobs: jobs.filter(job => job.customerName === customerName).length,
                        totalAmount: jobs.filter(job => job.customerName === customerName)
                                        .reduce((sum, job) => sum + calculateJobAmount(job), 0),
                        balance: 0,
                        createdAt: new Date().toISOString()
                    };
                    customers.push(customer);
                }
            });

            saveAccountingData();
        }

        // حساب مبلغ العمل باستخدام السعر المحدد
        function calculateJobAmount(job) {
            // استخدام السعر المخصص إذا كان متوفراً
            if (job.customAmount && job.customAmount > 0) {
                return job.customAmount;
            }

            // حساب باستخدام سعر الوحدة والكمية
            const unitPrice = parseFloat(job.unitPrice) || 0;
            const quantity = parseFloat(job.printedQuantity || job.totalQuantity) || 0;

            return unitPrice * quantity;
        }

        // تحويل الأرقام العربية إلى إنجليزية
        function convertToEnglishNumbers(text) {
            const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
            const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

            let result = text.toString();
            for (let i = 0; i < arabicNumbers.length; i++) {
                result = result.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
            }
            return result;
        }

        // تنسيق العملة (ريال يمني) بأرقام إنجليزية
        function formatCurrency(amount) {
            const formatted = (amount || 0).toLocaleString('en-US');
            return convertToEnglishNumbers(`${formatted} ريال يمني`);
        }

        // تنسيق العملة للعرض
        function formatCurrencyDisplay(amount) {
            return formatCurrency(amount);
        }

        // تنسيق الأرقام بالإنجليزية
        function formatNumber(number) {
            return convertToEnglishNumbers(number.toString());
        }

        // عرض الفواتير
        function displayInvoices() {
            const container = document.getElementById('invoicesList');
            
            if (invoices.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">📄</div>
                        <h3>لا توجد فواتير</h3>
                        <p>سيتم إنشاء الفواتير تلقائياً عند إضافة أرقام الفواتير في الأعمال</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = invoices.map(invoice => `
                <div class="invoice-card ${invoice.status}">
                    <div class="invoice-header">
                        <div class="invoice-number">${invoice.invoiceNumber}</div>
                        <div class="invoice-status status-${invoice.status}">
                            ${invoice.status === 'paid' ? 'مدفوعة' : 
                              invoice.status === 'pending' ? 'معلقة' : 'متأخرة'}
                        </div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">${invoice.customerName}</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العمل:</span>
                            <span class="invoice-detail-value">${invoice.jobName}</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الإنشاء:</span>
                            <span class="invoice-detail-value">${new Date(invoice.createdAt).toLocaleDateString('ar-EG')}</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الاستحقاق:</span>
                            <span class="invoice-detail-value">${new Date(invoice.dueDate).toLocaleDateString('ar-EG')}</span>
                        </div>
                    </div>
                    <div class="invoice-amount">${invoice.amount.toFixed(2)} ر.س</div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="viewInvoiceDetails(${invoice.id})">
                            📄 عرض التفاصيل
                        </button>
                        <button class="btn" onclick="sendInvoiceWhatsApp('${invoice.invoiceNumber}')" style="background: linear-gradient(135deg, #25d366, #128c7e); color: white;">
                            📱 واتساب
                        </button>
                        <button class="btn" onclick="generateInvoicePDF('${invoice.invoiceNumber}')" style="background: linear-gradient(135deg, #dc2626, #b91c1c); color: white;">
                            📄 PDF
                        </button>
                        ${invoice.status !== 'paid' ? `
                            <button class="btn btn-success" onclick="markInvoiceAsPaid(${invoice.id})">
                                ✅ تسجيل كمدفوعة
                            </button>
                        ` : ''}
                        <button class="btn btn-danger" onclick="deleteInvoice(${invoice.id})">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // عرض العملاء
        function displayCustomers() {
            const container = document.getElementById('customersList');
            
            if (customers.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">👤</div>
                        <h3>لا يوجد عملاء</h3>
                        <p>سيتم إضافة العملاء تلقائياً عند تسجيلهم في الأعمال</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = customers.map(customer => {
                const customerInvoices = invoices.filter(inv => inv.customerName === customer.name);
                const totalPaid = payments.filter(p => p.customerName === customer.name)
                                         .reduce((sum, p) => sum + p.amount, 0);
                const totalDue = customerInvoices.reduce((sum, inv) => sum + inv.amount, 0);
                const balance = totalDue - totalPaid;

                return `
                    <div class="customer-card">
                        <div class="customer-header">
                            <div class="customer-name">${customer.name}</div>
                            <div class="customer-balance ${balance > 0 ? 'balance-negative' : balance < 0 ? 'balance-positive' : 'balance-zero'}">
                                ${balance.toFixed(2)} ر.س
                            </div>
                        </div>
                        <div class="invoice-details">
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">إجمالي الأعمال:</span>
                                <span class="invoice-detail-value">${customer.totalJobs}</span>
                            </div>
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">إجمالي الفواتير:</span>
                                <span class="invoice-detail-value">${totalDue.toFixed(2)} ر.س</span>
                            </div>
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">إجمالي المدفوع:</span>
                                <span class="invoice-detail-value">${totalPaid.toFixed(2)} ر.س</span>
                            </div>
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">تاريخ التسجيل:</span>
                                <span class="invoice-detail-value">${new Date(customer.createdAt).toLocaleDateString('ar-EG')}</span>
                            </div>
                        </div>
                        <div class="job-actions">
                            <button class="btn btn-primary" onclick="viewCustomerDetails('${customer.name}')">
                                👤 عرض التفاصيل
                            </button>
                            <button class="btn btn-success" onclick="addPaymentForCustomer('${customer.name}')">
                                💰 إضافة دفعة
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // عرض المدفوعات
        function displayPayments() {
            const container = document.getElementById('paymentsList');
            
            if (payments.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">💳</div>
                        <h3>لا توجد مدفوعات</h3>
                        <p>استخدم النموذج أعلاه لتسجيل دفعة جديدة</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = payments.map(payment => `
                <div class="payment-card">
                    <div class="payment-header">
                        <div class="payment-amount">${payment.amount.toFixed(2)} ر.س</div>
                        <div class="payment-method">${getPaymentMethodText(payment.method)}</div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">${payment.customerName}</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">رقم الفاتورة:</span>
                            <span class="invoice-detail-value">${payment.invoiceNumber}</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">تاريخ الدفع:</span>
                            <span class="invoice-detail-value">${new Date(payment.createdAt).toLocaleDateString('ar-EG')}</span>
                        </div>
                        ${payment.notes ? `
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">ملاحظات:</span>
                                <span class="invoice-detail-value">${payment.notes}</span>
                            </div>
                        ` : ''}
                    </div>
                    <div class="job-actions">
                        <button class="btn btn-danger" onclick="deletePayment(${payment.id})">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // تحديث الإحصائيات المحاسبية
        function updateAccountingStats() {
            const totalRevenue = invoices.reduce((sum, inv) => sum + inv.amount, 0);
            const paidInvoices = invoices.filter(inv => inv.status === 'paid').length;
            const pendingPayments = invoices.filter(inv => inv.status === 'pending').length;
            const totalCustomers = customers.length;

            document.getElementById('total-revenue').textContent = totalRevenue.toFixed(2);
            document.getElementById('paid-invoices').textContent = paidInvoices;
            document.getElementById('pending-payments').textContent = pendingPayments;
            document.getElementById('total-customers').textContent = totalCustomers;
        }

        // التركيز على عميل في النظام المحاسبي
        function focusOnCustomerInAccounting(customerName) {
            // الانتقال لتبويب العملاء
            switchAccountingTab('customers');
            
            // البحث عن العميل وتمييزه
            setTimeout(() => {
                const customerCards = document.querySelectorAll('.customer-card');
                customerCards.forEach(card => {
                    const nameElement = card.querySelector('.customer-name');
                    if (nameElement && nameElement.textContent === customerName) {
                        card.style.border = '3px solid #3b82f6';
                        card.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.3)';
                        card.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                });
            }, 500);
        }

        // حفظ بيانات النظام المحاسبي
        function saveAccountingData() {
            localStorage.setItem('modernPrintingInvoices', JSON.stringify(invoices));
            localStorage.setItem('modernPrintingPayments', JSON.stringify(payments));
            localStorage.setItem('modernPrintingCustomers', JSON.stringify(customers));
        }

        // دوال مساعدة
        function getPaymentMethodText(method) {
            const methods = {
                'cash': 'نقداً',
                'bank': 'تحويل بنكي',
                'check': 'شيك',
                'card': 'بطاقة ائتمان'
            };
            return methods[method] || method;
        }

        // دوال الأحداث
        function markInvoiceAsPaid(invoiceId) {
            const invoice = invoices.find(inv => inv.id === invoiceId);
            if (invoice) {
                invoice.status = 'paid';
                saveAccountingData();
                displayInvoices();
                updateAccountingStats();
                showMessage('✅ تم تسجيل الفاتورة كمدفوعة', 'success');
            }
        }

        function deleteInvoice(invoiceId) {
            if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
                invoices = invoices.filter(inv => inv.id !== invoiceId);
                saveAccountingData();
                displayInvoices();
                updateAccountingStats();
                showMessage('✅ تم حذف الفاتورة', 'success');
            }
        }

        function deletePayment(paymentId) {
            if (confirm('هل أنت متأكد من حذف هذه الدفعة؟')) {
                payments = payments.filter(p => p.id !== paymentId);
                saveAccountingData();
                displayPayments();
                updateAccountingStats();
                showMessage('✅ تم حذف الدفعة', 'success');
            }
        }

        // إضافة دفعة جديدة
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const payment = {
                id: Date.now(),
                customerName: document.getElementById('paymentCustomer').value,
                invoiceNumber: document.getElementById('paymentInvoice').value,
                amount: parseFloat(document.getElementById('paymentAmount').value),
                method: document.getElementById('paymentMethod').value,
                notes: document.getElementById('paymentNotes').value,
                createdAt: new Date().toISOString()
            };

            payments.push(payment);
            saveAccountingData();
            displayPayments();
            updateAccountingStats();
            
            // إعادة تعيين النموذج
            this.reset();
            
            showMessage('✅ تم تسجيل الدفعة بنجاح', 'success');
        });

        // تحديث قوائم العملاء والفواتير في نموذج المدفوعات
        function updatePaymentFormOptions() {
            const customerSelect = document.getElementById('paymentCustomer');
            const invoiceSelect = document.getElementById('paymentInvoice');
            
            // تحديث قائمة العملاء
            customerSelect.innerHTML = '<option value="">اختر العميل...</option>' +
                customers.map(customer => `<option value="${customer.name}">${customer.name}</option>`).join('');
            
            // تحديث قائمة الفواتير
            customerSelect.addEventListener('change', function() {
                const selectedCustomer = this.value;
                const customerInvoices = invoices.filter(inv => inv.customerName === selectedCustomer && inv.status !== 'paid');
                
                invoiceSelect.innerHTML = '<option value="">اختر الفاتورة...</option>' +
                    customerInvoices.map(inv => `<option value="${inv.invoiceNumber}">${inv.invoiceNumber} - ${inv.amount.toFixed(2)} ر.س</option>`).join('');
            });
        }

        // تقارير بسيطة
        function generateDailyReport() {
            const today = new Date().toDateString();
            const todayPayments = payments.filter(p => new Date(p.createdAt).toDateString() === today);
            const todayAmount = todayPayments.reduce((sum, p) => sum + p.amount, 0);
            
            document.getElementById('reportContent').innerHTML = `
                <div class="report-section">
                    <div class="report-title">تقرير اليوم - ${new Date().toLocaleDateString('ar-EG')}</div>
                    <div class="report-summary">
                        <div class="summary-item">
                            <div class="summary-value">${todayPayments.length}</div>
                            <div class="summary-label">عدد المدفوعات</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-value">${todayAmount.toFixed(2)}</div>
                            <div class="summary-label">إجمالي المبلغ (ر.س)</div>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateMonthlyReport() {
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();
            const monthlyPayments = payments.filter(p => {
                const paymentDate = new Date(p.createdAt);
                return paymentDate.getMonth() === currentMonth && paymentDate.getFullYear() === currentYear;
            });
            const monthlyAmount = monthlyPayments.reduce((sum, p) => sum + p.amount, 0);
            
            document.getElementById('reportContent').innerHTML = `
                <div class="report-section">
                    <div class="report-title">تقرير الشهر - ${new Date().toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' })}</div>
                    <div class="report-summary">
                        <div class="summary-item">
                            <div class="summary-value">${monthlyPayments.length}</div>
                            <div class="summary-label">عدد المدفوعات</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-value">${monthlyAmount.toFixed(2)}</div>
                            <div class="summary-label">إجمالي المبلغ (ر.س)</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // ==================== دوال النظام المحاسبي المتقدم ====================

        // لوحة التحكم
        function updateDashboard() {
            const totalRevenue = invoices.reduce((sum, inv) => sum + inv.amount, 0);
            const pendingAmount = invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.amount, 0);
            const totalCustomers = customers.length;
            
            // إيرادات الشهر الحالي
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();
            const monthlyRevenue = receipts.filter(r => {
                const receiptDate = new Date(r.createdAt);
                return receiptDate.getMonth() === currentMonth && receiptDate.getFullYear() === currentYear;
            }).reduce((sum, r) => sum + r.amount, 0);

            document.getElementById('dashboard-total-revenue').textContent = totalRevenue.toFixed(2);
            document.getElementById('dashboard-pending-amount').textContent = pendingAmount.toFixed(2);
            document.getElementById('dashboard-total-customers').textContent = totalCustomers;
            document.getElementById('dashboard-monthly-revenue').textContent = monthlyRevenue.toFixed(2);

            // أحدث الفواتير
            const recentInvoices = invoices.slice(-5).reverse();
            document.getElementById('recent-invoices').innerHTML = recentInvoices.length > 0 ? 
                recentInvoices.map(inv => `
                    <div style="padding: 10px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between;">
                        <span>${inv.invoiceNumber} - ${inv.customerName}</span>
                        <span style="color: #059669; font-weight: bold;">${inv.amount.toFixed(2)} ر.س</span>
                    </div>
                `).join('') : '<p style="text-align: center; color: #64748b;">لا توجد فواتير حديثة</p>';

            // أحدث المعاملات
            const recentTransactions = [...receipts, ...paymentsOut].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)).slice(0, 5);
            document.getElementById('recent-transactions').innerHTML = recentTransactions.length > 0 ?
                recentTransactions.map(trans => `
                    <div style="padding: 10px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between;">
                        <span>${trans.receiptNumber || trans.paymentNumber} - ${trans.customerName || trans.supplierName}</span>
                        <span style="color: ${trans.receiptNumber ? '#059669' : '#dc2626'}; font-weight: bold;">
                            ${trans.receiptNumber ? '+' : '-'}${trans.amount.toFixed(2)} ر.س
                        </span>
                    </div>
                `).join('') : '<p style="text-align: center; color: #64748b;">لا توجد معاملات حديثة</p>';
        }

        // إدارة العملاء والموردين
        function updateCustomerFormOptions() {
            // تحديث قوائم العملاء في النماذج المختلفة
            const customerSelects = ['receiptCustomer', 'paymentSupplier', 'statementCustomer', 'reportCustomer'];
            customerSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    const currentValue = select.value;
                    select.innerHTML = '<option value="">اختر...</option>' +
                        customers.map(customer => `<option value="${customer.name}">${customer.name} (${customer.type === 'customer' ? 'عميل' : 'مورد'})</option>`).join('');
                    select.value = currentValue;
                }
            });
            
            // تحديث اقتراحات العملاء في جميع بطاقات الأعمال المفتوحة
            refreshAllCustomerSuggestions();
        }

        // تحديث اقتراحات العملاء في جميع البطاقات
        function refreshAllCustomerSuggestions() {
            // إغلاق جميع قوائم الاقتراحات المفتوحة لإعادة تحميلها
            const allSuggestions = document.querySelectorAll('.customer-suggestions');
            allSuggestions.forEach(suggestion => {
                suggestion.style.display = 'none';
            });
        }

        // إضافة عميل/مورد جديد
        document.getElementById('customerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const customer = {
                id: Date.now(),
                name: document.getElementById('customerName').value.trim(),
                type: document.getElementById('customerType').value,
                phone: document.getElementById('customerPhone').value.trim(),
                email: document.getElementById('customerEmail').value.trim(),
                address: document.getElementById('customerAddress').value.trim(),
                balance: parseFloat(document.getElementById('customerBalance').value) || 0,
                totalJobs: 0,
                totalAmount: 0,
                createdAt: new Date().toISOString()
            };

            customers.push(customer);
            saveAccountingData();
            displayCustomers();
            updateCustomerFormOptions();
            
            this.reset();
            showMessage(`✅ تم إضافة ${customer.type === 'customer' ? 'العميل' : 'المورد'} بنجاح`, 'success');
        });

        // عرض العملاء المحدث
        function displayCustomers() {
            const container = document.getElementById('customersList');
            const typeFilter = document.getElementById('customerTypeFilter').value;
            const searchTerm = document.getElementById('customerSearch').value.toLowerCase();
            
            let filteredCustomers = customers;
            
            if (typeFilter !== 'all') {
                filteredCustomers = filteredCustomers.filter(c => c.type === typeFilter);
            }
            
            if (searchTerm) {
                filteredCustomers = filteredCustomers.filter(c => 
                    c.name.toLowerCase().includes(searchTerm) ||
                    c.phone.includes(searchTerm) ||
                    c.email.toLowerCase().includes(searchTerm)
                );
            }
            
            if (filteredCustomers.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">👤</div>
                        <h3>لا يوجد ${typeFilter === 'customer' ? 'عملاء' : typeFilter === 'supplier' ? 'موردين' : 'حسابات'}</h3>
                        <p>استخدم النموذج أعلاه لإضافة ${typeFilter === 'customer' ? 'عميل' : typeFilter === 'supplier' ? 'مورد' : 'حساب'} جديد</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredCustomers.map(customer => {
                const customerReceipts = receipts.filter(r => r.customerName === customer.name);
                const customerPayments = paymentsOut.filter(p => p.supplierName === customer.name);
                const totalReceived = customerReceipts.reduce((sum, r) => sum + r.amount, 0);
                const totalPaid = customerPayments.reduce((sum, p) => sum + p.amount, 0);
                const balance = customer.type === 'customer' ? (customer.balance + totalReceived) : (customer.balance - totalPaid);

                return `
                    <div class="customer-card">
                        <div class="customer-header">
                            <div class="customer-name">
                                ${customer.name} 
                                <span style="background: ${customer.type === 'customer' ? '#dbeafe' : '#fef3c7'}; 
                                             color: ${customer.type === 'customer' ? '#1d4ed8' : '#d97706'}; 
                                             padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">
                                    ${customer.type === 'customer' ? 'عميل' : 'مورد'}
                                </span>
                            </div>
                            <div class="customer-balance ${balance > 0 ? 'balance-positive' : balance < 0 ? 'balance-negative' : 'balance-zero'}">
                                ${balance.toFixed(2)} ر.س
                            </div>
                        </div>
                        <div class="invoice-details">
                            ${customer.phone ? `
                                <div class="invoice-detail">
                                    <span class="invoice-detail-label">الهاتف:</span>
                                    <span class="invoice-detail-value">${customer.phone}</span>
                                </div>
                            ` : ''}
                            ${customer.email ? `
                                <div class="invoice-detail">
                                    <span class="invoice-detail-label">البريد:</span>
                                    <span class="invoice-detail-value">${customer.email}</span>
                                </div>
                            ` : ''}
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">إجمالي المعاملات:</span>
                                <span class="invoice-detail-value">${customer.type === 'customer' ? customerReceipts.length : customerPayments.length}</span>
                            </div>
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">تاريخ التسجيل:</span>
                                <span class="invoice-detail-value">${new Date(customer.createdAt).toLocaleDateString('ar-EG')}</span>
                            </div>
                        </div>
                        <div class="job-actions">
                            <button class="btn btn-primary" onclick="generateStatement('${customer.name}')">
                                📋 كشف الحساب
                            </button>
                            <button class="btn" onclick="sendCustomerStatementWhatsApp('${customer.name}')" style="background: linear-gradient(135deg, #25d366, #128c7e); color: white;">
                                📱 واتساب
                            </button>
                            <button class="btn btn-success" onclick="editCustomer(${customer.id})">
                                ✏️ تعديل
                            </button>
                            <button class="btn btn-danger" onclick="deleteCustomer(${customer.id})">
                                🗑️ حذف
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // فلترة العملاء
        function filterCustomers() {
            displayCustomers();
        }

        function searchCustomers() {
            displayCustomers();
        }

        // سندات القبض
        function generateReceiptNumber() {
            receiptCounter++;
            localStorage.setItem('modernPrintingReceiptCounter', receiptCounter);
            const receiptNumber = `REC-${receiptCounter.toString().padStart(4, '0')}`;
            document.getElementById('receiptNumber').value = receiptNumber;
            return receiptNumber;
        }

        function updateReceiptFormOptions() {
            const customerSelect = document.getElementById('receiptCustomer');
            const customerCustomers = customers.filter(c => c.type === 'customer');
            customerSelect.innerHTML = '<option value="">اختر العميل...</option>' +
                customerCustomers.map(customer => `<option value="${customer.name}">${customer.name}</option>`).join('');
        }

        document.getElementById('receiptForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const receipt = {
                id: Date.now(),
                receiptNumber: document.getElementById('receiptNumber').value,
                customerName: document.getElementById('receiptCustomer').value,
                amount: parseFloat(document.getElementById('receiptAmount').value),
                method: document.getElementById('receiptMethod').value,
                description: document.getElementById('receiptDescription').value,
                notes: document.getElementById('receiptNotes').value,
                createdAt: new Date().toISOString()
            };

            receipts.push(receipt);
            saveAccountingData();
            displayReceipts();
            generateReceiptNumber();
            
            this.reset();
            document.getElementById('receiptNumber').value = generateReceiptNumber();
            
            showMessage('✅ تم إضافة سند القبض بنجاح', 'success');
        });

        function displayReceipts() {
            const container = document.getElementById('receiptsList');
            
            if (receipts.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">📥</div>
                        <h3>لا توجد سندات قبض</h3>
                        <p>استخدم النموذج أعلاه لإضافة سند قبض جديد</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = receipts.map(receipt => `
                <div class="receipt-card">
                    <div class="receipt-header">
                        <div class="receipt-number">${receipt.receiptNumber}</div>
                        <div class="receipt-amount">+${receipt.amount.toFixed(2)} ر.س</div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">العميل:</span>
                            <span class="invoice-detail-value">${receipt.customerName}</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">طريقة الاستلام:</span>
                            <span class="invoice-detail-value">${getPaymentMethodText(receipt.method)}</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">التاريخ:</span>
                            <span class="invoice-detail-value">${new Date(receipt.createdAt).toLocaleDateString('ar-EG')}</span>
                        </div>
                        ${receipt.description ? `
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">البيان:</span>
                                <span class="invoice-detail-value">${receipt.description}</span>
                            </div>
                        ` : ''}
                    </div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="printReceipt(${receipt.id})">
                            🖨️ طباعة السند
                        </button>
                        <button class="btn btn-danger" onclick="deleteReceipt(${receipt.id})">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // سندات الصرف
        function generatePaymentNumber() {
            paymentCounter++;
            localStorage.setItem('modernPrintingPaymentCounter', paymentCounter);
            const paymentNumber = `PAY-${paymentCounter.toString().padStart(4, '0')}`;
            document.getElementById('paymentNumber').value = paymentNumber;
            return paymentNumber;
        }

        function updatePaymentFormOptions() {
            const supplierSelect = document.getElementById('paymentSupplier');
            const suppliers = customers.filter(c => c.type === 'supplier');
            supplierSelect.innerHTML = '<option value="">اختر المورد...</option>' +
                suppliers.map(supplier => `<option value="${supplier.name}">${supplier.name}</option>`).join('');
        }

        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const payment = {
                id: Date.now(),
                paymentNumber: document.getElementById('paymentNumber').value,
                supplierName: document.getElementById('paymentSupplier').value,
                amount: parseFloat(document.getElementById('paymentAmountOut').value),
                method: document.getElementById('paymentMethodOut').value,
                description: document.getElementById('paymentDescription').value,
                notes: document.getElementById('paymentNotesOut').value,
                createdAt: new Date().toISOString()
            };

            paymentsOut.push(payment);
            saveAccountingData();
            displayPaymentsOut();
            generatePaymentNumber();
            
            this.reset();
            document.getElementById('paymentNumber').value = generatePaymentNumber();
            
            showMessage('✅ تم إضافة سند الصرف بنجاح', 'success');
        });

        function displayPaymentsOut() {
            const container = document.getElementById('paymentsList');
            
            if (paymentsOut.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">📤</div>
                        <h3>لا توجد سندات صرف</h3>
                        <p>استخدم النموذج أعلاه لإضافة سند صرف جديد</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = paymentsOut.map(payment => `
                <div class="payment-out-card">
                    <div class="receipt-header">
                        <div class="receipt-number">${payment.paymentNumber}</div>
                        <div class="payment-out-amount">-${payment.amount.toFixed(2)} ر.س</div>
                    </div>
                    <div class="invoice-details">
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">المورد:</span>
                            <span class="invoice-detail-value">${payment.supplierName}</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">طريقة الدفع:</span>
                            <span class="invoice-detail-value">${getPaymentMethodText(payment.method)}</span>
                        </div>
                        <div class="invoice-detail">
                            <span class="invoice-detail-label">التاريخ:</span>
                            <span class="invoice-detail-value">${new Date(payment.createdAt).toLocaleDateString('ar-EG')}</span>
                        </div>
                        ${payment.description ? `
                            <div class="invoice-detail">
                                <span class="invoice-detail-label">البيان:</span>
                                <span class="invoice-detail-value">${payment.description}</span>
                            </div>
                        ` : ''}
                    </div>
                    <div class="job-actions">
                        <button class="btn btn-primary" onclick="printPayment(${payment.id})">
                            🖨️ طباعة السند
                        </button>
                        <button class="btn btn-danger" onclick="deletePaymentOut(${payment.id})">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // كشوف الحسابات
        function updateStatementFormOptions() {
            const customerSelect = document.getElementById('statementCustomer');
            customerSelect.innerHTML = '<option value="">اختر العميل أو المورد...</option>' +
                customers.map(customer => `<option value="${customer.name}">${customer.name} (${customer.type === 'customer' ? 'عميل' : 'مورد'})</option>`).join('');
        }

        function generateStatement(customerName = null) {
            const selectedCustomer = customerName || document.getElementById('statementCustomer').value;
            if (!selectedCustomer) {
                showMessage('⚠️ يرجى اختيار العميل أولاً', 'warning');
                return;
            }

            const customer = customers.find(c => c.name === selectedCustomer);
            if (!customer) return;

            const customerReceipts = receipts.filter(r => r.customerName === selectedCustomer);
            const customerPayments = paymentsOut.filter(p => p.supplierName === selectedCustomer);
            const customerInvoices = invoices.filter(inv => inv.customerName === selectedCustomer);

            // دمج جميع المعاملات وترتيبها حسب التاريخ
            const transactions = [
                ...customerReceipts.map(r => ({...r, type: 'receipt', date: new Date(r.createdAt)})),
                ...customerPayments.map(p => ({...p, type: 'payment', date: new Date(p.createdAt)})),
                ...customerInvoices.map(inv => ({...inv, type: 'invoice', date: new Date(inv.createdAt)}))
            ].sort((a, b) => a.date - b.date);

            let runningBalance = customer.balance || 0;
            
            const statementHTML = `
                <div class="report-section">
                    <!-- Header with Logo -->
                    <div style="text-align: center; padding: 20px 0; background: linear-gradient(135deg, #1e293b 0%, #**********%); color: white; border: 3px solid #475569; border-radius: 10px; margin-bottom: 25px;">
                        <div style="font-size: 36px; margin-bottom: 8px;">${systemSettings.companyLogo}</div>
                        <h2 style="margin: 0; font-size: 24px; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">${systemSettings.companyName}</h2>
                        <p style="margin: 5px 0 0 0; font-size: 14px; opacity: 0.9;">للطباعة والتصميم</p>
                        <p style="margin: 5px 0 0 0; font-size: 12px; opacity: 0.8;">📍 ${systemSettings.companyAddress} | 📞 ${systemSettings.companyPhone}</p>
                    </div>

                    <div class="report-title" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white; padding: 15px; border-radius: 8px; text-align: center; margin-bottom: 20px;">
                        📋 كشف حساب: ${selectedCustomer}
                    </div>
                    <div style="text-align: center; margin-bottom: 20px; color: #64748b; background: #f8fafc; padding: 10px; border-radius: 6px;">
                        📅 التاريخ: ${convertToEnglishNumbers(new Date().toLocaleDateString('en-US'))} | 👤 نوع الحساب: ${customer.type === 'customer' ? 'عميل' : 'مورد'}
                    </div>
                    
                    <table class="statement-table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>البيان</th>
                                <th>رقم المستند</th>
                                <th>مدين</th>
                                <th>دائن</th>
                                <th>الرصيد</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background: #f8fafc; font-weight: bold;">
                                <td colspan="5">الرصيد الافتتاحي</td>
                                <td>${convertToEnglishNumbers(runningBalance.toFixed(2))}</td>
                            </tr>
                            ${transactions.map(trans => {
                                let debit = 0, credit = 0, description = '';
                                
                                if (trans.type === 'receipt') {
                                    credit = trans.amount;
                                    runningBalance += credit;
                                    description = trans.description || 'سند قبض';
                                } else if (trans.type === 'payment') {
                                    debit = trans.amount;
                                    runningBalance -= debit;
                                    description = trans.description || 'سند صرف';
                                } else if (trans.type === 'invoice') {
                                    if (customer.type === 'customer') {
                                        debit = trans.amount;
                                        runningBalance += debit;
                                    } else {
                                         credit = trans.amount;
                                        runningBalance -= credit;
                    
                      }
                             description = `فاتورة: ${trans.jobName || 'غير محدد'}`;
                
                      }
                                                          const clickHandler = trans.type === 'invoice' ?
                                    `onclick="editInvoiceFromStatement('${trans.invoiceNumber}')" style="cursor: pointer;" title="اضغط لتعديل الفاتورة"` : '';

                                return `
                                    <tr ${clickHandler} class="${trans.type === 'invoice' ? 'invoice-row-clickable' : ''}">
                                        <td>${convertToEnglishNumbers(trans.date.toLocaleDateString('en-US'))}</td>
                                        <td>${description}</td>
                                        <td>${trans.receiptNumber || trans.paymentNumber || trans.invoiceNumber}</td>
                                        <td>${debit > 0 ? formatCurrency(debit) : '-'}</td>
                                        <td>${credit > 0 ? formatCurrency(credit) : '-'}</td>
                                        <td>${formatCurrency(runningBalance)}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                    
                    <div class="statement-balance ${runningBalance < 0 ? 'negative' : ''}">
                        الرصيد النهائي: ${formatCurrency(runningBalance)}
                    </div>
                </div>
            `;

            document.getElementById('statementContent').innerHTML = statementHTML;
        }

        function printStatement() {
            const content = document.getElementById('statementContent').innerHTML;
            if (!content || content.includes('empty-state')) {
                showMessage('⚠️ يرجى إنشاء كشف الحساب أولاً', 'warning');
                return;
            }

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>كشف حساب</title>
                        <style>
                            body { font-family: Arial, sans-serif; direction: rtl; }
                            .statement-table { width: 100%; border-collapse: collapse; }
                            .statement-table th, .statement-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                            .statement-table th { background: #f5f5f5; }
                            .statement-balance { text-align: center; font-size: 18px; font-weight: bold; margin: 20px 0; }
                        </style>
                    </head>
                    <body>${content}</body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // إرسال كشف الحساب عبر WhatsApp
        function sendStatementWhatsApp() {
            const selectedCustomer = document.getElementById('statementCustomer').value;
            if (!selectedCustomer) {
                showMessage('⚠️ يرجى اختيار العميل أولاً', 'warning');
                return;
            }

            const customer = customers.find(c => c.name === selectedCustomer);
            if (!customer) {
                showMessage('❌ لم يتم العثور على بيانات العميل', 'error');
                return;
            }

            // حساب الرصيد النهائي
            const customerReceipts = receipts.filter(r => r.customerName === selectedCustomer);
            const customerPayments = paymentsOut.filter(p => p.supplierName === selectedCustomer);
            const customerInvoices = invoices.filter(inv => inv.customerName === selectedCustomer);

            let finalBalance = customer.balance || 0;

            // حساب الرصيد النهائي
            customerReceipts.forEach(r => finalBalance += r.amount);
            customerPayments.forEach(p => finalBalance -= p.amount);
            customerInvoices.forEach(inv => {
                if (customer.type === 'customer') {
                    finalBalance += inv.amount;
                } else {
                    finalBalance -= inv.amount;
                }
            });

            // إنشاء رسالة WhatsApp مفصلة
            const currentDate = convertToEnglishNumbers(new Date().toLocaleDateString('en-US'));
            const balanceStatus = finalBalance >= 0 ? 'دائن' : 'مدين';
            const balanceColor = finalBalance >= 0 ? '🟢' : '🔴';

            const message = `
╔══════════════════════════════╗
║      📋 *كشف حساب*           ║
╚══════════════════════════════╝

🏢 *${systemSettings.companyName}*
📍 ${systemSettings.companyAddress}
📞 ${systemSettings.companyPhone}

┌─────────────────────────────┐
│  👤 *العميل:* ${selectedCustomer}
│  📅 *التاريخ:* ${currentDate}
│  🏷️ *نوع الحساب:* ${customer.type === 'customer' ? 'عميل' : 'مورد'}
└─────────────────────────────┘

📊 *ملخص الحساب:*
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

💰 *إجمالي الفواتير:* ${customerInvoices.length} فاتورة
📥 *إجمالي المقبوضات:* ${customerReceipts.length} سند
📤 *إجمالي المدفوعات:* ${customerPayments.length} سند

╔══════════════════════════════╗
║  ${balanceColor} *الرصيد النهائي*        ║
║     ${formatCurrency(Math.abs(finalBalance))} (${balanceStatus})     ║
╚══════════════════════════════╝

${finalBalance < 0 ? '⚠️ *يوجد مبلغ مستحق للدفع*' : '✅ *الحساب في وضع جيد*'}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📱 *للاستفسار:* ${systemSettings.companyPhone}
📧 *البريد:* ${systemSettings.companyEmail}
🌐 *الموقع:* ${systemSettings.companyWebsite}

🙏 *شكراً لتعاملكم معنا*
💫 *${systemSettings.companyName} - خدمة متميزة*
            `.trim();

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showMessage('✅ تم فتح WhatsApp لإرسال كشف الحساب', 'success');
        }

        // إرسال كشف حساب عميل مباشرة من قائمة العملاء
        function sendCustomerStatementWhatsApp(customerName) {
            const customer = customers.find(c => c.name === customerName);
            if (!customer) {
                showMessage('❌ لم يتم العثور على بيانات العميل', 'error');
                return;
            }

            // حساب الرصيد والمعاملات
            const customerReceipts = receipts.filter(r => r.customerName === customerName);
            const customerPayments = paymentsOut.filter(p => p.supplierName === customerName);
            const customerInvoices = invoices.filter(inv => inv.customerName === customerName);

            let finalBalance = customer.balance || 0;

            // حساب الرصيد النهائي
            customerReceipts.forEach(r => finalBalance += r.amount);
            customerPayments.forEach(p => finalBalance -= p.amount);
            customerInvoices.forEach(inv => {
                if (customer.type === 'customer') {
                    finalBalance += inv.amount;
                } else {
                    finalBalance -= inv.amount;
                }
            });

            // إنشاء رسالة مختصرة وسريعة
            const currentDate = convertToEnglishNumbers(new Date().toLocaleDateString('en-US'));
            const balanceStatus = finalBalance >= 0 ? 'دائن' : 'مدين';
            const balanceIcon = finalBalance >= 0 ? '✅' : '⚠️';

            const message = `
${systemSettings.companyLogo} *${systemSettings.companyName}*

📋 *كشف حساب سريع*
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

👤 *العميل:* ${customerName}
📅 *التاريخ:* ${currentDate}
🏷️ *النوع:* ${customer.type === 'customer' ? 'عميل' : 'مورد'}

📊 *ملخص سريع:*
• الفواتير: ${customerInvoices.length}
• المقبوضات: ${customerReceipts.length}
• المدفوعات: ${customerPayments.length}

${balanceIcon} *الرصيد النهائي:*
${formatCurrency(Math.abs(finalBalance))} (${balanceStatus})

${finalBalance < 0 ? '💳 *يرجى تسوية المبلغ المستحق*' : '🎉 *الحساب محدث ومتوازن*'}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📱 ${systemSettings.companyPhone}
🏢 ${systemSettings.companyName}
            `.trim();

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showMessage(`✅ تم فتح WhatsApp لإرسال كشف حساب ${customerName}`, 'success');
        }

        // إرسال فاتورة من قائمة الفواتير عبر WhatsApp
        function sendInvoiceWhatsApp(invoiceNumber) {
            // البحث عن الأعمال المرتبطة بهذا الرقم
            const relatedJobs = jobs.filter(job => job.invoiceNumber === invoiceNumber);

            if (relatedJobs.length === 0) {
                showMessage('❌ لم يتم العثور على أعمال مرتبطة بهذا الرقم', 'error');
                return;
            }

            const mainJob = relatedJobs[0];

            // استخدام نفس وظيفة إرسال الفاتورة الموجودة
            notifyCustomerOnCompletion(mainJob.id);
        }

        // إنشاء وتحميل فاتورة PDF
        async function generateInvoicePDF(invoiceNumber) {
            try {
                // البحث عن الأعمال المرتبطة بهذا الرقم
                const relatedJobs = jobs.filter(job => job.invoiceNumber === invoiceNumber);

                if (relatedJobs.length === 0) {
                    showMessage('❌ لم يتم العثور على أعمال مرتبطة بهذا الرقم', 'error');
                    return;
                }

                const mainJob = relatedJobs[0];

                showMessage('⏳ جاري إنشاء ملف PDF...', 'info');

                // إنشاء محتوى الفاتورة
                const invoiceContent = generateInvoiceContent(mainJob);

                // إنشاء عنصر مؤقت لعرض الفاتورة
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = invoiceContent;
                tempDiv.style.position = 'absolute';
                tempDiv.style.left = '-9999px';
                tempDiv.style.top = '-9999px';
                tempDiv.style.width = '800px';
                tempDiv.style.background = 'white';
                tempDiv.style.padding = '20px';
                document.body.appendChild(tempDiv);

                // تحويل إلى صورة باستخدام html2canvas
                const canvas = await html2canvas(tempDiv, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    width: 800,
                    height: tempDiv.scrollHeight
                });

                // إزالة العنصر المؤقت
                document.body.removeChild(tempDiv);

                // إنشاء PDF باستخدام jsPDF
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });

                // حساب أبعاد الصورة للـ PDF
                const imgWidth = 190; // عرض A4 بالمليمتر مع هوامش
                const imgHeight = (canvas.height * imgWidth) / canvas.width;

                // إضافة الصورة للـ PDF
                pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 10, 10, imgWidth, imgHeight);

                // حفظ الملف
                const fileName = `فاتورة_${invoiceNumber}_${mainJob.customerName || 'عميل'}.pdf`;
                pdf.save(fileName);

                showMessage('✅ تم إنشاء وتحميل ملف PDF بنجاح!', 'success');

            } catch (error) {
                console.error('خطأ في إنشاء PDF:', error);
                showMessage('❌ حدث خطأ في إنشاء ملف PDF', 'error');
            }
        }

        // إرسال فاتورة PDF عبر WhatsApp
        async function sendInvoicePDFWhatsApp(invoiceNumber) {
            try {
                // البحث عن الأعمال المرتبطة بهذا الرقم
                const relatedJobs = jobs.filter(job => job.invoiceNumber === invoiceNumber);

                if (relatedJobs.length === 0) {
                    showMessage('❌ لم يتم العثور على أعمال مرتبطة بهذا الرقم', 'error');
                    return;
                }

                const mainJob = relatedJobs[0];

                // إنشاء رسالة مع رابط لتحميل PDF
                const message = `
${systemSettings.companyLogo} *${systemSettings.companyName}*

📄 *فاتورة PDF جاهزة للتحميل*
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📋 *رقم الفاتورة:* ${invoiceNumber}
👤 *العميل:* ${mainJob.customerName || 'غير محدد'}
📅 *التاريخ:* ${convertToEnglishNumbers(new Date(mainJob.createdAt).toLocaleDateString('en-US'))}

💰 *المبلغ الإجمالي:*
${formatCurrency(relatedJobs.reduce((sum, job) => sum + calculateJobAmount(job), 0))}

📱 *لتحميل الفاتورة بصيغة PDF:*
يرجى الضغط على الرابط أدناه أو طلب إرسال الملف مباشرة

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🏢 ${systemSettings.companyName}
📞 ${systemSettings.companyPhone}
📍 ${systemSettings.companyAddress}

🙏 *شكراً لتعاملكم معنا*
                `.trim();

                const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
                window.open(whatsappUrl, '_blank');

                // أيضاً إنشاء PDF للتحميل
                await generateInvoicePDF(invoiceNumber);

                showMessage('✅ تم فتح WhatsApp وإنشاء ملف PDF', 'success');

            } catch (error) {
                console.error('خطأ في إرسال PDF:', error);
                showMessage('❌ حدث خطأ في إرسال الفاتورة', 'error');
            }
        }

        // إنشاء وتحميل كشف حساب PDF
        async function generateStatementPDF() {
            try {
                const selectedCustomer = document.getElementById('statementCustomer').value;
                if (!selectedCustomer) {
                    showMessage('⚠️ يرجى اختيار العميل أولاً', 'warning');
                    return;
                }

                const content = document.getElementById('statementContent').innerHTML;
                if (!content || content.includes('empty-state')) {
                    showMessage('⚠️ يرجى إنشاء كشف الحساب أولاً', 'warning');
                    return;
                }

                showMessage('⏳ جاري إنشاء ملف PDF...', 'info');

                // إنشاء عنصر مؤقت لعرض كشف الحساب
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = content;
                tempDiv.style.position = 'absolute';
                tempDiv.style.left = '-9999px';
                tempDiv.style.top = '-9999px';
                tempDiv.style.width = '800px';
                tempDiv.style.background = 'white';
                tempDiv.style.padding = '20px';
                tempDiv.style.fontFamily = 'Arial, sans-serif';
                tempDiv.style.direction = 'rtl';
                document.body.appendChild(tempDiv);

                // تحويل إلى صورة باستخدام html2canvas
                const canvas = await html2canvas(tempDiv, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    width: 800,
                    height: tempDiv.scrollHeight
                });

                // إزالة العنصر المؤقت
                document.body.removeChild(tempDiv);

                // إنشاء PDF باستخدام jsPDF
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });

                // حساب أبعاد الصورة للـ PDF
                const imgWidth = 190; // عرض A4 بالمليمتر مع هوامش
                const imgHeight = (canvas.height * imgWidth) / canvas.width;

                // إضافة الصورة للـ PDF
                pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 10, 10, imgWidth, imgHeight);

                // حفظ الملف
                const fileName = `كشف_حساب_${selectedCustomer}_${new Date().toLocaleDateString('en-US').replace(/\//g, '-')}.pdf`;
                pdf.save(fileName);

                showMessage('✅ تم إنشاء وتحميل كشف الحساب PDF بنجاح!', 'success');

            } catch (error) {
                console.error('خطأ في إنشاء PDF:', error);
                showMessage('❌ حدث خطأ في إنشاء ملف PDF', 'error');
            }
        }

        // التقارير المتقدمة
        function updateReportFormOptions() {
            const customerSelect = document.getElementById('reportCustomer');
            if (customerSelect) {
                customerSelect.innerHTML = '<option value="">جميع العملاء والموردين</option>' +
                    customers.map(customer => `<option value="${customer.name}">${customer.name}</option>`).join('');
            }
            
            // تعيين التواريخ الافتراضية
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            
            const fromDateInput = document.getElementById('reportFromDate');
            const toDateInput = document.getElementById('reportToDate');
            
            if (fromDateInput) fromDateInput.value = firstDay.toISOString().split('T')[0];
            if (toDateInput) toDateInput.value = today.toISOString().split('T')[0];
        }

        // حفظ البيانات المحدث
        function saveAccountingData() {
            localStorage.setItem('modernPrintingInvoices', JSON.stringify(invoices));
            localStorage.setItem('modernPrintingPayments', JSON.stringify(payments));
            localStorage.setItem('modernPrintingCustomers', JSON.stringify(customers));
            localStorage.setItem('modernPrintingReceipts', JSON.stringify(receipts));
            localStorage.setItem('modernPrintingPaymentsOut', JSON.stringify(paymentsOut));
            localStorage.setItem('modernPrintingReceiptCounter', receiptCounter);
            localStorage.setItem('modernPrintingPaymentCounter', paymentCounter);
        }

        // دوال الحذف والطباعة
        function deleteReceipt(receiptId) {
            if (confirm('هل أنت متأكد من حذف سند القبض؟')) {
                receipts = receipts.filter(r => r.id !== receiptId);
                saveAccountingData();
                displayReceipts();
                showMessage('✅ تم حذف سند القبض', 'success');
            }
        }

        function deletePaymentOut(paymentId) {
            if (confirm('هل أنت متأكد من حذف سند الصرف؟')) {
                paymentsOut = paymentsOut.filter(p => p.id !== paymentId);
                saveAccountingData();
                displayPaymentsOut();
                showMessage('✅ تم حذف سند الصرف', 'success');
            }
        }

        function deleteCustomer(customerId) {
            if (confirm('هل أنت متأكد من حذف هذا الحساب؟ سيتم حذف جميع المعاملات المرتبطة به.')) {
                const customer = customers.find(c => c.id === customerId);
                if (customer) {
                    // حذف المعاملات المرتبطة
                    receipts = receipts.filter(r => r.customerName !== customer.name);
                    paymentsOut = paymentsOut.filter(p => p.supplierName !== customer.name);
                    
                    // حذف العميل
                    customers = customers.filter(c => c.id !== customerId);
                    
                    saveAccountingData();
                    displayCustomers();
                    updateCustomerFormOptions();
                    showMessage('✅ تم حذف الحساب وجميع معاملاته', 'success');
                }
            }
        }

        function printReceipt(receiptId) {
            const receipt = receipts.find(r => r.id === receiptId);
            if (!receipt) return;
            
            const printContent = `
                <div style="text-align: center; font-family: Arial, sans-serif; direction: rtl;">
                    <h2>سند قبض</h2>
                    <p><strong>رقم السند:</strong> ${receipt.receiptNumber}</p>
                    <p><strong>التاريخ:</strong> ${new Date(receipt.createdAt).toLocaleDateString('ar-EG')}</p>
                    <hr>
                    <p><strong>استلمنا من السيد/ة:</strong> ${receipt.customerName}</p>
                    <p><strong>مبلغ وقدره:</strong> ${formatCurrency(receipt.amount)}</p>
                    <p><strong>طريقة الاستلام:</strong> ${getPaymentMethodText(receipt.method)}</p>
                    ${receipt.description ? `<p><strong>البيان:</strong> ${receipt.description}</p>` : ''}
                    ${receipt.notes ? `<p><strong>ملاحظات:</strong> ${receipt.notes}</p>` : ''}
                    <br><br>
                    <p>التوقيع: ________________</p>
                </div>
            `;
            
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`<html><head><title>سند قبض</title></head><body>${printContent}</body></html>`);
            printWindow.document.close();
            printWindow.print();
        }

        function printPayment(paymentId) {
            const payment = paymentsOut.find(p => p.id === paymentId);
            if (!payment) return;
            
            const printContent = `
                <div style="text-align: center; font-family: Arial, sans-serif; direction: rtl;">
                    <h2>سند صرف</h2>
                    <p><strong>رقم السند:</strong> ${payment.paymentNumber}</p>
                    <p><strong>التاريخ:</strong> ${new Date(payment.createdAt).toLocaleDateString('ar-EG')}</p>
                    <hr>
                    <p><strong>دفعنا للسيد/ة:</strong> ${payment.supplierName}</p>
                    <p><strong>مبلغ وقدره:</strong> ${formatCurrency(payment.amount)}</p>
                    <p><strong>طريقة الدفع:</strong> ${getPaymentMethodText(payment.method)}</p>
                    ${payment.description ? `<p><strong>البيان:</strong> ${payment.description}</p>` : ''}
                    ${payment.notes ? `<p><strong>ملاحظات:</strong> ${payment.notes}</p>` : ''}
                    <br><br>
                    <p>التوقيع: ________________</p>
                </div>
            `;
            
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`<html><head><title>سند صرف</title></head><body>${printContent}</body></html>`);
            printWindow.document.close();
            printWindow.print();
        }

        // تحديث إحصائيات العميل عند ربطه بعمل
        function updateCustomerJobStats(customerName) {
            if (!customerName) return;

            const customer = customers.find(c => c.name === customerName);
            if (customer) {
                const customerJobs = jobs.filter(job => job.customerName === customerName);
                customer.totalJobs = customerJobs.length;
                customer.totalAmount = customerJobs.reduce((sum, job) => sum + calculateJobAmount(job), 0);
                saveAccountingData();
            }
        }

        // تعديل الفاتورة من كشف الحساب
        function editInvoiceFromStatement(invoiceNumber) {
            const job = jobs.find(j => j.invoiceNumber === invoiceNumber);
            if (!job) {
                showMessage('❌ لم يتم العثور على الفاتورة', 'error');
                return;
            }

            // إنشاء نافذة تعديل الفاتورة
            const editModal = document.createElement('div');
            editModal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                direction: rtl;
            `;

            editModal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 16px;
                    padding: 30px;
                    max-width: 700px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                ">
                    <div style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 25px;
                        padding-bottom: 15px;
                        border-bottom: 2px solid #e2e8f0;
                    ">
                        <h2 style="
                            color: #1e293b;
                            margin: 0;
                            font-size: 24px;
                        ">📝 تعديل الفاتورة: ${invoiceNumber}</h2>
                        <button onclick="closeInvoiceEditModal()" style="
                            background: #ef4444;
                            color: white;
                            border: none;
                            border-radius: 50%;
                            width: 35px;
                            height: 35px;
                            cursor: pointer;
                            font-size: 18px;
                        ">×</button>
                    </div>

                    <form onsubmit="saveInvoiceEdit(event, '${invoiceNumber}')">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">اسم العمل:</label>
                                <input type="text" id="invoice_edit_jobName" value="${job.name}" style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                ">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">اسم العميل:</label>
                                <input type="text" id="invoice_edit_customerName" value="${job.customerName || ''}" style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                ">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">الكمية:</label>
                                <input type="text" id="invoice_edit_quantity" value="${job.printedQuantity || job.totalQuantity || ''}" style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                ">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">المبلغ (ريال يمني):</label>
                                <input type="number" id="invoice_edit_amount" value="${calculateJobAmount(job)}" style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                ">
                            </div>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">تفاصيل العمل:</label>
                            <textarea id="invoice_edit_details" rows="3" style="
                                width: 100%;
                                padding: 12px 16px;
                                border: 2px solid #e5e7eb;
                                border-radius: 8px;
                                font-size: 16px;
                                resize: vertical;
                            ">${job.details || ''}</textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: center; margin-top: 30px; flex-wrap: wrap;">
                            <button type="submit" style="
                                background: linear-gradient(135deg, #10b981, #059669);
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 8px;
                                font-weight: 600;
                                cursor: pointer;
                                font-size: 14px;
                            ">💾 حفظ</button>
                            <button type="button" onclick="generateInvoicePDF('${invoiceNumber}')" style="
                                background: linear-gradient(135deg, #ef4444, #dc2626);
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 8px;
                                font-weight: 600;
                                cursor: pointer;
                                font-size: 14px;
                            ">📄 PDF</button>
                            <button type="button" onclick="sendInvoiceWhatsApp('${invoiceNumber}')" style="
                                background: linear-gradient(135deg, #10b981, #059669);
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 8px;
                                font-weight: 600;
                                cursor: pointer;
                                font-size: 14px;
                            ">📱 واتساب</button>
                            <button type="button" onclick="closeInvoiceEditModal()" style="
                                background: linear-gradient(135deg, #6b7280, #4b5563);
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 8px;
                                font-weight: 600;
                                cursor: pointer;
                                font-size: 14px;
                            ">❌ إلغاء</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(editModal);
        }

        // تعديل الفاتورة من قائمة الأعمال
        function editInvoiceFromJobsList(invoiceNumber) {
            const job = jobs.find(j => j.invoiceNumber === invoiceNumber);
            if (!job) {
                showMessage('❌ لم يتم العثور على الفاتورة', 'error');
                return;
            }

            // البحث عن العمل وإظهار نموذج التعديل
            const jobCard = document.querySelector(`[data-job-id="${job.id}"]`);
            if (jobCard) {
                jobCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
                jobCard.style.border = '3px solid #10b981';
                jobCard.style.boxShadow = '0 0 20px rgba(16, 185, 129, 0.3)';
                
                // إزالة التمييز بعد 3 ثوان
                setTimeout(() => {
                    jobCard.style.border = '';
                    jobCard.style.boxShadow = '';
                }, 3000);
            }
            
            showMessage('✅ يمكنك الآن تعديل بيانات الفاتورة', 'success');
        }

        // إظهار خيارات المشاركة
        function showShareOptions(jobId, customerName) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            // إنشاء عنصر خيارات المشاركة
            const shareOptionsHtml = `
                <div id="shareOptions_${jobId}" class="share-actions">
                    <h4 style="margin: 0 0 15px 0; color: #1e293b; text-align: center;">
                        🎉 تم حفظ العميل بنجاح! اختر إجراء:
                    </h4>
                    <button class="share-btn print" onclick="printJobInvoice(${jobId})">
                        🖨️ طباعة الفاتورة
                    </button>
                    <button class="share-btn whatsapp" onclick="shareViaWhatsApp(${jobId})">
                        📱 إرسال واتساب
                    </button>
                    <button class="share-btn pdf" onclick="generatePDF(${jobId})">
                        📄 تحويل لـ PDF
                    </button>
                    <button class="btn btn-secondary" onclick="hideShareOptions(${jobId})" style="margin-top: 10px; width: 100%;">
                        ❌ إغلاق
                    </button>
                </div>
            `;

            // البحث عن بطاقة العمل وإضافة خيارات المشاركة
            const jobCard = document.querySelector(`[data-job-id="${jobId}"]`);
            if (jobCard) {
                // إزالة خيارات المشاركة السابقة إن وجدت
                const existingOptions = jobCard.querySelector(`#shareOptions_${jobId}`);
                if (existingOptions) {
                    existingOptions.remove();
                }

                // إضافة خيارات المشاركة الجديدة
                jobCard.insertAdjacentHTML('beforeend', shareOptionsHtml);
                
                // التمرير إلى العنصر
                jobCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        // إخفاء خيارات المشاركة
        function hideShareOptions(jobId) {
            const shareOptions = document.getElementById(`shareOptions_${jobId}`);
            if (shareOptions) {
                shareOptions.remove();
            }
        }

        // طباعة فاتورة العمل
        function printJobInvoice(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) {
                showMessage('❌ لم يتم العثور على العمل', 'error');
                return;
            }

            const invoiceContent = generateInvoiceContent(job);
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>فاتورة - ${job.invoiceNumber}</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                        .invoice-header { text-align: center; margin-bottom: 30px; }
                        .invoice-details { margin-bottom: 20px; }
                        .invoice-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        .invoice-table th, .invoice-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        .invoice-table th { background-color: #f5f5f5; }
                        .total { font-weight: bold; font-size: 18px; margin-top: 20px; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${invoiceContent}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
            
            hideShareOptions(jobId);
            showMessage('✅ تم إرسال الفاتورة للطباعة', 'success');
        }

        // مشاركة عبر واتساب
        function shareViaWhatsApp(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) {
                showMessage('❌ لم يتم العثور على العمل', 'error');
                return;
            }

            // عرض خيارات المشاركة
            showWhatsAppShareOptions(jobId, job);
        }

        // عرض خيارات مشاركة الواتساب
        function showWhatsAppShareOptions(jobId, job) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content whatsapp-share-modal">
                    <div class="modal-header">
                        <h3>📱 خيارات مشاركة الفاتورة</h3>
                        <button class="close-btn" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="share-options-grid">
                            <button class="share-option-btn text-option" onclick="shareAsText(${jobId})">
                                <div class="option-icon">📝</div>
                                <div class="option-title">نص منسق</div>
                                <div class="option-desc">مشاركة كنص منسق بشكل جميل</div>
                            </button>
                            
                            <button class="share-option-btn image-option" onclick="shareAsImage(${jobId})">
                                <div class="option-icon">🖼️</div>
                                <div class="option-title">صورة</div>
                                <div class="option-desc">تحويل الفاتورة إلى صورة</div>
                            </button>
                            
                            <button class="share-option-btn pdf-option" onclick="shareAsPDF(${jobId})">
                                <div class="option-icon">📄</div>
                                <div class="option-title">ملف PDF</div>
                                <div class="option-desc">إنشاء ملف PDF للفاتورة</div>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // مشاركة كنص محسن
        function shareAsText(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            const message = generateEnhancedWhatsAppMessage(job);
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            document.querySelector('.modal-overlay').remove();
            hideShareOptions(jobId);
            showMessage('✅ تم فتح واتساب لإرسال الفاتورة كنص', 'success');
        }

        // مشاركة كصورة
        function shareAsImage(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            generateInvoiceImage(job).then(imageBlob => {
                // إنشاء رابط تحميل مؤقت
                const url = URL.createObjectURL(imageBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `فاتورة_${job.invoiceNumber || job.id}.png`;
                a.click();
                URL.revokeObjectURL(url);

                // فتح واتساب مع رسالة مرفقة
                const message = `🖨️ فاتورة طباعة\n\n👤 العميل: ${job.customerName}\n📝 العمل: ${job.name}\n💰 المبلغ: ${formatCurrency(calculateJobAmount(job))}\n\n📎 الفاتورة مرفقة كصورة`;
                const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
                window.open(whatsappUrl, '_blank');

                document.querySelector('.modal-overlay').remove();
                hideShareOptions(jobId);
                showMessage('✅ تم تحميل صورة الفاتورة وفتح واتساب', 'success');
            }).catch(error => {
                console.error('خطأ في إنشاء الصورة:', error);
                showMessage('❌ حدث خطأ في إنشاء صورة الفاتورة', 'error');
            });
        }

        // مشاركة كـ PDF
        function shareAsPDF(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            generateInvoicePDF(job).then(() => {
                // فتح واتساب مع رسالة مرفقة
                const message = `🖨️ فاتورة طباعة\n\n👤 العميل: ${job.customerName}\n📝 العمل: ${job.name}\n💰 المبلغ: ${formatCurrency(calculateJobAmount(job))}\n\n📎 الفاتورة مرفقة كملف PDF`;
                const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
                window.open(whatsappUrl, '_blank');

                document.querySelector('.modal-overlay').remove();
                hideShareOptions(jobId);
                showMessage('✅ تم تحميل ملف PDF وفتح واتساب', 'success');
            }).catch(error => {
                console.error('خطأ في إنشاء PDF:', error);
                showMessage('❌ حدث خطأ في إنشاء ملف PDF', 'error');
            });
        }

        // تحويل إلى PDF
        function generatePDF(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) {
                showMessage('❌ لم يتم العثور على العمل', 'error');
                return;
            }

            // استخدام html2pdf إذا كان متوفراً، وإلا استخدام الدالة الجديدة
            if (typeof html2pdf !== 'undefined') {
                const invoiceContent = generateInvoiceHTML(job);
                const element = document.createElement('div');
                element.innerHTML = invoiceContent;

                html2pdf()
                    .from(element)
                    .set({
                        margin: 1,
                        filename: `فاتورة_${job.invoiceNumber || job.id}.pdf`,
                        image: { type: 'jpeg', quality: 0.98 },
                        html2canvas: { scale: 2 },
                        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
                    })
                    .save();
                
                hideShareOptions(jobId);
                showMessage('✅ تم تحويل الفاتورة إلى PDF', 'success');
            } else {
                // استخدام الدالة الجديدة
                generateInvoicePDF(job).then(() => {
                    hideShareOptions(jobId);
                    showMessage('✅ تم إنشاء ملف PDF للفاتورة', 'success');
                }).catch(error => {
                    console.error('خطأ في إنشاء PDF:', error);
                    showMessage('❌ حدث خطأ في إنشاء ملف PDF', 'error');
                });
            }
        }

        // إنشاء محتوى الفاتورة المحسن
        function generateInvoiceContent(job) {
            const theme = systemSettings.invoiceTheme || 'modern';
            const color = systemSettings.invoiceColor || '#3b82f6';
            const headerStyle = systemSettings.invoiceHeaderStyle || 'gradient';
            const showLogo = systemSettings.showLogo !== false;
            const showBorder = systemSettings.showBorder !== false;
            const showFooterMessage = systemSettings.showFooterMessage !== false;
            const showCompanyDetails = systemSettings.showCompanyDetails !== false;
            const footerMessage = systemSettings.footerMessage || '🙏 شكراً لثقتكم بنا';

            // تحديد ألوان التصميم حسب النمط
            let headerBackground, borderColor, accentColor;
            switch (theme) {
                case 'classic':
                    headerBackground = headerStyle === 'gradient' ? `linear-gradient(135deg, ${color} 0%, #1e293b 100%)` : color;
                    borderColor = '#1e293b';
                    accentColor = '#1e293b';
                    break;
                case 'elegant':
                    headerBackground = headerStyle === 'gradient' ? `linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)` : '#8b5cf6';
                    borderColor = '#8b5cf6';
                    accentColor = '#8b5cf6';
                    break;
                case 'minimal':
                    headerBackground = headerStyle === 'gradient' ? `linear-gradient(135deg, #64748b 0%, #**********%)` : '#64748b';
                    borderColor = '#e5e7eb';
                    accentColor = '#64748b';
                    break;
                default: // modern
                    headerBackground = headerStyle === 'gradient' ? `linear-gradient(135deg, ${color} 0%, #2563eb 100%)` : color;
                    borderColor = color;
                    accentColor = color;
            }

            const borderStyle = showBorder ? `border: 3px solid ${borderColor}; border-radius: 15px;` : '';
            const headerBorderRadius = showBorder ? 'border-radius: 15px 15px 0 0;' : 'border-radius: 10px;';

            return `
                <div style="max-width: 800px; margin: 0 auto; font-family: 'Arial', sans-serif; direction: rtl; background: white; ${borderStyle}">
                    <!-- Header with Logo -->
                    <div style="text-align: center; padding: 30px 0; background: ${headerBackground}; color: white; ${headerBorderRadius}">
                        ${showLogo ? `<div style="font-size: 48px; margin-bottom: 10px;">${systemSettings.companyLogo}</div>` : ''}
                        <h1 style="margin: 0; font-size: 32px; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">${systemSettings.companyName}</h1>
                        <p style="margin: 5px 0 0 0; font-size: 16px; opacity: 0.9;">للطباعة والتصميم</p>
                        ${showCompanyDetails ? `<p style="margin: 5px 0 0 0; font-size: 14px; opacity: 0.8;">📍 ${systemSettings.companyAddress} | 📞 ${systemSettings.companyPhone}</p>` : ''}
                    </div>

                    <!-- Invoice Info -->
                    <div style="background: #f8fafc; padding: 25px; border-left: 5px solid ${accentColor};">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <h3 style="color: ${accentColor}; margin: 0 0 10px 0; font-size: 18px;">📋 معلومات الفاتورة</h3>
                                <p style="margin: 5px 0; font-size: 16px;"><strong>رقم الفاتورة:</strong> ${job.invoiceNumber || 'غير محدد'}</p>
                                <p style="margin: 5px 0; font-size: 16px;"><strong>التاريخ:</strong> ${convertToEnglishNumbers(new Date(job.createdAt).toLocaleDateString('en-US'))}</p>
                            </div>
                            <div>
                                <h3 style="color: ${accentColor}; margin: 0 0 10px 0; font-size: 18px;">👤 بيانات العميل</h3>
                                <p style="margin: 5px 0; font-size: 16px;"><strong>اسم العميل:</strong> ${job.customerName || 'غير محدد'}</p>
                                <p style="margin: 5px 0; font-size: 16px;"><strong>حالة العمل:</strong> مكتمل ومسلم ✅</p>
                            </div>
                        </div>
                    </div>

                    <!-- Services Table -->
                    <div style="margin: 25px 0;">
                        <h3 style="color: ${accentColor}; margin: 0 0 15px 0; font-size: 20px; text-align: center; border-bottom: 3px double ${accentColor}; padding-bottom: 10px;">📝 تفاصيل الخدمة</h3>
                        <table style="width: 100%; border-collapse: collapse; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border: 3px solid ${accentColor}; border-radius: 10px; overflow: hidden;">
                            <thead>
                                <tr style="background: ${headerBackground}; color: white; border-bottom: 2px solid ${borderColor};">
                                    <th style="padding: 15px; text-align: center; font-size: 16px; font-weight: bold; border-right: 2px solid rgba(255,255,255,0.3);">الوصف</th>
                                    <th style="padding: 15px; text-align: center; font-size: 16px; font-weight: bold; border-right: 2px solid rgba(255,255,255,0.3);">الكمية</th>
                                    <th style="padding: 15px; text-align: center; font-size: 16px; font-weight: bold; border-right: 2px solid rgba(255,255,255,0.3);">سعر الوحدة</th>
                                    <th style="padding: 15px; text-align: center; font-size: 16px; font-weight: bold;">المجموع</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="background: #f9fafb;">
                                    <td style="padding: 20px; text-align: center; font-size: 16px; border-bottom: 2px dashed #cbd5e1; border-right: 2px solid #e5e7eb;">${job.name}</td>
                                    <td style="padding: 20px; text-align: center; font-size: 16px; border-bottom: 2px dashed #cbd5e1; border-right: 2px solid #e5e7eb;">${job.printedQuantity || job.totalQuantity || 'غير محدد'}</td>
                                    <td style="padding: 20px; text-align: center; font-size: 16px; border-bottom: 2px dashed #cbd5e1; border-right: 2px solid #e5e7eb;">${formatCurrency((job.unitPrice || 0))}</td>
                                    <td style="padding: 20px; text-align: center; font-size: 16px; font-weight: bold; color: #059669; border-bottom: 2px dashed #cbd5e1;">${formatCurrency(calculateJobAmount(job))}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    ${job.details ? `
                    <div style="background: #fef3c7; padding: 20px; border-radius: 10px; border-left: 5px solid #f59e0b; margin: 20px 0;">
                        <h4 style="margin: 0 0 10px 0; color: #92400e; font-size: 16px;">📋 تفاصيل إضافية:</h4>
                        <p style="margin: 0; color: #92400e; font-size: 14px;">${job.details}</p>
                    </div>
                    ` : ''}

                    <!-- Total -->
                    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 25px; border-radius: 15px; text-align: center; margin: 25px 0;">
                        <h2 style="margin: 0; font-size: 24px;">💰 إجمالي المبلغ المستحق</h2>
                        <p style="margin: 10px 0 0 0; font-size: 32px; font-weight: bold;">${formatCurrency(calculateJobAmount(job))}</p>
                    </div>

                    ${showFooterMessage ? `
                    <!-- Footer -->
                    <div style="text-align: center; padding: 20px; background: #f1f5f9; border: 2px solid #e2e8f0; ${showBorder ? 'border-radius: 0 0 15px 15px;' : 'border-radius: 10px;'} color: #64748b;">
                        <p style="margin: 0; font-size: 16px; font-weight: bold;">${footerMessage}</p>
                        <p style="margin: 5px 0 0 0; font-size: 14px;">💫 جودة عالية - أسعار منافسة - خدمة متميزة</p>
                        ${showCompanyDetails ? `
                        <div style="margin-top: 15px; padding-top: 15px; border-top: 2px dashed #cbd5e1;">
                            <p style="margin: 0; font-size: 12px;">📧 ${systemSettings.companyEmail} | 🌐 ${systemSettings.companyWebsite}</p>
                        </div>
                        ` : ''}
                    </div>
                    ` : ''}
                </div>
            `;
        }

        // تحديث دالة quickUpdateJob لتشمل ربط العملاء
        const originalQuickUpdateJob = window.quickUpdateJob;
        window.quickUpdateJob = function(jobId, field, value) {
            // استدعاء الدالة الأصلية
            if (originalQuickUpdateJob) {
                originalQuickUpdateJob(jobId, field, value);
            }

            // إذا كان التحديث خاص بالعميل، قم بحفظه فوراً وتحديث الإحصائيات
            if (field === 'customer') {
                const job = jobs.find(j => j.id === jobId);
                if (job) {
                    const oldCustomerName = job.customerName;
                    job.customerName = value;
                    saveJobs();
                    
                    // تحديث النظام المحاسبي
                    updateAccountingData();
                    
                    // تحديث إحصائيات العميل الجديد
                    if (value) {
                        updateCustomerJobStats(value);
                    }
                    
                    // تحديث إحصائيات العميل القديم
                    if (oldCustomerName && oldCustomerName !== value) {
                        updateCustomerJobStats(oldCustomerName);
                    }
                }
            }
        };

        // حفظ تعديلات الفاتورة
        function saveInvoiceEdit(event, invoiceNumber) {
            event.preventDefault();

            const job = jobs.find(j => j.invoiceNumber === invoiceNumber);
            if (!job) return;

            // تحديث بيانات العمل
            job.name = document.getElementById('invoice_edit_jobName').value;
            job.customerName = document.getElementById('invoice_edit_customerName').value;
            job.printedQuantity = document.getElementById('invoice_edit_quantity').value;
            job.totalQuantity = document.getElementById('invoice_edit_quantity').value;
            job.details = document.getElementById('invoice_edit_details').value;
            job.customAmount = parseFloat(document.getElementById('invoice_edit_amount').value) || 0;
            job.lastModified = new Date().toISOString();

            // تحديث الفاتورة في النظام المحاسبي
            const invoice = invoices.find(inv => inv.invoiceNumber === invoiceNumber);
            if (invoice) {
                invoice.amount = job.customAmount || calculateJobAmount(job);
                invoice.jobName = job.name;
                invoice.customerName = job.customerName;
            }

            saveData();
            saveAccountingData();
            closeInvoiceEditModal();
            showMessage('✅ تم حفظ تعديلات الفاتورة بنجاح!', 'success');

            // تحديث كشف الحساب إذا كان مفتوحاً
            const currentCustomer = document.getElementById('statementCustomer')?.value;
            if (currentCustomer) {
                generateStatement(currentCustomer);
            }
        }

        // إغلاق نافذة تعديل الفاتورة
        function closeInvoiceEditModal() {
            const modal = document.querySelector('div[style*="position: fixed"]');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // إنشاء PDF للفاتورة
        function generateInvoicePDF(invoiceNumber) {
            const job = jobs.find(j => j.invoiceNumber === invoiceNumber);
            if (!job) {
                showMessage('❌ لم يتم العثور على الفاتورة', 'error');
                return;
            }

            const invoiceContent = generateInvoiceContent(job);

            // إنشاء عنصر مؤقت للطباعة
            const printElement = document.createElement('div');
            printElement.innerHTML = invoiceContent;
            printElement.style.direction = 'rtl';
            printElement.style.fontFamily = 'Arial, sans-serif';
            printElement.style.padding = '20px';
            printElement.style.backgroundColor = 'white';

            // محاولة استخدام html2pdf إذا كان متوفراً
            if (typeof html2pdf !== 'undefined') {
                html2pdf()
                    .from(printElement)
                    .set({
                        margin: 1,
                        filename: `فاتورة_${invoiceNumber}.pdf`,
                        image: { type: 'jpeg', quality: 0.98 },
                        html2canvas: { scale: 2 },
                        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
                    })
                    .save();

                showMessage('✅ تم إنشاء ملف PDF للفاتورة', 'success');
            } else {
                // البديل: إنشاء ملف HTML للتحميل
                const htmlContent = `
                    <!DOCTYPE html>
                    <html dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>فاتورة ${invoiceNumber}</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; }
                            .invoice-header { text-align: center; margin-bottom: 30px; }
                            .invoice-details { margin-bottom: 20px; }
                            .invoice-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                            .invoice-table th, .invoice-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                            .invoice-table th { background-color: #f5f5f5; }
                            .total { font-weight: bold; font-size: 18px; margin-top: 20px; }
                        </style>
                    </head>
                    <body>
                        ${invoiceContent}
                    </body>
                    </html>
                `;

                const blob = new Blob([htmlContent], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `فاتورة_${invoiceNumber}.html`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                showMessage('✅ تم تحميل الفاتورة كملف HTML', 'success');
            }
        }

        // إرسال الفاتورة عبر WhatsApp
        function sendInvoiceWhatsApp(invoiceNumber) {
            const job = jobs.find(j => j.invoiceNumber === invoiceNumber);
            if (!job) {
                showMessage('❌ لم يتم العثور على الفاتورة', 'error');
                return;
            }

            // البحث عن أعمال أخرى بنفس رقم الفاتورة
            const relatedJobs = jobs.filter(j => j.invoiceNumber === invoiceNumber && j.id !== job.id);

            // إنشاء رسالة WhatsApp مفصلة وجذابة
            let jobsList = `📝 *${job.name}*`;
            if (job.details) {
                jobsList += `\n   └ ${job.details}`;
            }

            // إضافة الأعمال المرتبطة
            if (relatedJobs.length > 0) {
                relatedJobs.forEach(relatedJob => {
                    jobsList += `\n📝 *${relatedJob.name}*`;
                    if (relatedJob.details) {
                        jobsList += `\n   └ ${relatedJob.details}`;
                    }
                });
            }

            // حساب المبلغ الإجمالي لجميع الأعمال
            const totalAmount = [job, ...relatedJobs].reduce((sum, j) => sum + calculateJobAmount(j), 0);
            const totalQuantity = [job, ...relatedJobs].reduce((sum, j) => sum + parseInt(j.printedQuantity || j.totalQuantity || 0), 0);

            const message = `
╔══════════════════════════════╗
║        🖨️ *فاتورة طباعة*        ║
╚══════════════════════════════╝

🏢 *${systemSettings.companyName}*
📍 ${systemSettings.companyAddress}
📞 ${systemSettings.companyPhone}

┌─────────────────────────────┐
│  📋 *رقم الفاتورة:* ${invoiceNumber}
│  👤 *العميل:* ${job.customerName || 'غير محدد'}
│  📅 *التاريخ:* ${convertToEnglishNumbers(new Date(job.createdAt).toLocaleDateString('en-US'))}
│  📊 *إجمالي الكمية:* ${convertToEnglishNumbers(totalQuantity.toString())} قطعة
└─────────────────────────────┘

📋 *تفاصيل الأعمال:*
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
${jobsList}

${relatedJobs.length > 0 ? `\n📊 *عدد الأعمال:* ${relatedJobs.length + 1} عمل\n` : ''}

╔══════════════════════════════╗
║  💰 *إجمالي المبلغ المستحق*     ║
║     ${formatCurrency(totalAmount)}     ║
╚══════════════════════════════╝

✅ *حالة الأعمال:* مكتملة ومسلمة
🎯 *جودة عالية - خدمة سريعة*

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📱 *للاستفسار:* ${systemSettings.companyPhone}
📧 *البريد:* ${systemSettings.companyEmail}
🌐 *الموقع:* ${systemSettings.companyWebsite}

🙏 *شكراً لثقتكم بنا*
💫 *${systemSettings.companyName} - شريككم في النجاح*
            `.trim();

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showMessage('✅ تم فتح WhatsApp لإرسال الفاتورة', 'success');

            // إغلاق النافذة إذا كانت مفتوحة
            closeInvoiceEditModal();
        }

        // إضافة إشعار تلقائي عند اكتمال العمل
        function notifyCustomerOnCompletion(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job || !job.customerName) return;

            // إنشاء إشعار
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
                padding: 20px;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
                z-index: 10001;
                max-width: 400px;
                direction: rtl;
                animation: slideIn 0.5s ease-out;
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="font-size: 24px;">🎉</div>
                    <div>
                        <h4 style="margin: 0 0 8px 0; font-size: 16px;">تم اكتمال العمل!</h4>
                        <p style="margin: 0; font-size: 14px; opacity: 0.9;">
                            العمل: ${job.name}<br>
                            العميل: ${job.customerName}
                        </p>
                        <div style="margin-top: 12px; display: flex; gap: 8px;">
                            <button onclick="sendInvoiceWhatsApp('${job.invoiceNumber}')" style="
                                background: rgba(255,255,255,0.2);
                                color: white;
                                border: none;
                                padding: 6px 12px;
                                border-radius: 6px;
                                font-size: 12px;
                                cursor: pointer;
                            ">📱 إرسال واتساب</button>
                            <button onclick="generateInvoicePDF('${job.invoiceNumber}')" style="
                                background: rgba(255,255,255,0.2);
                                color: white;
                                border: none;
                                padding: 6px 12px;
                                border-radius: 6px;
                                font-size: 12px;
                                cursor: pointer;
                            ">📄 PDF</button>
                            <button onclick="this.parentElement.parentElement.parentElement.parentElement.remove()" style="
                                background: rgba(255,255,255,0.2);
                                color: white;
                                border: none;
                                padding: 6px 12px;
                                border-radius: 6px;
                                font-size: 12px;
                                cursor: pointer;
                            ">✕</button>
                        </div>
                    </div>
                </div>
            `;

            // إضافة CSS للحركة
            if (!document.getElementById('notification-styles')) {
                const style = document.createElement('style');
                style.id = 'notification-styles';
                style.textContent = `
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // إزالة الإشعار تلقائياً بعد 10 ثوان
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 10000);
        }

        // إعدادات النظام
        let systemSettings = JSON.parse(localStorage.getItem('modernPrintingSettings')) || {
            companyName: 'مطبعة الحديثة',
            companyPhone: '*********',
            companyAddress: 'صنعاء - اليمن',
            companyEmail: '<EMAIL>',
            companyWebsite: 'www.modernprint.ye',
            companyLogo: '🖨️',
            defaultCurrency: 'ريال يمني',
            // إعدادات تصميم الفاتورة
            invoiceTheme: 'modern', // modern, classic, elegant, minimal
            invoiceColor: '#3b82f6', // اللون الأساسي
            invoiceHeaderStyle: 'gradient', // gradient, solid, image
            showLogo: true,
            showBorder: true,
            showFooterMessage: true,
            footerMessage: '🙏 شكراً لثقتكم بنا',
            showCompanyDetails: true,
            hideFromWhatsApp: ['printerName'], // العناصر المخفية من رسالة الواتساب
            whatsappStyle: 'modern', // modern, classic, simple, business
            whatsappHeader: '🖨️ *فاتورة طباعة*',
            whatsappFooter: '🙏 شكراً لثقتكم بنا',
            showCompanyInfoInWhatsApp: true,
            showBordersInWhatsApp: true,
            dateFormat: 'en-US',
            numberFormat: 'english'
        };

        // حفظ إعدادات الشركة
        function saveCompanySettings() {
            systemSettings.companyName = document.getElementById('companyName').value || 'مطبعة الحديثة';
            systemSettings.companyPhone = document.getElementById('companyPhone').value || '*********';
            systemSettings.companyAddress = document.getElementById('companyAddress').value || 'صنعاء - اليمن';
            systemSettings.companyEmail = document.getElementById('companyEmail').value || '<EMAIL>';
            systemSettings.companyWebsite = document.getElementById('companyWebsite').value || 'www.modernprint.ye';
            systemSettings.companyLogo = document.getElementById('companyLogo').value || '🖨️';

            localStorage.setItem('modernPrintingSettings', JSON.stringify(systemSettings));
            showMessage('✅ تم حفظ إعدادات الشركة بنجاح!', 'success');
            updateInvoicePreview();
        }

        // حفظ إعدادات النظام
        function saveSystemSettings() {
            systemSettings.defaultCurrency = document.getElementById('defaultCurrency').value;
            systemSettings.dateFormat = document.getElementById('dateFormat').value;
            systemSettings.numberFormat = document.getElementById('numberFormat').value;

            localStorage.setItem('modernPrintingSettings', JSON.stringify(systemSettings));
            showMessage('✅ تم حفظ إعدادات النظام بنجاح!', 'success');
        }

        // حفظ إعدادات تصميم الفاتورة
        function saveInvoiceDesignSettings() {
            systemSettings.invoiceTheme = document.getElementById('invoiceTheme').value;
            systemSettings.invoiceColor = document.getElementById('invoiceColor').value;
            systemSettings.invoiceHeaderStyle = document.getElementById('invoiceHeaderStyle').value;
            systemSettings.footerMessage = document.getElementById('footerMessage').value;
            systemSettings.showLogo = document.getElementById('showLogo').checked;
            systemSettings.showBorder = document.getElementById('showBorder').checked;
            systemSettings.showFooterMessage = document.getElementById('showFooterMessage').checked;
            systemSettings.showCompanyDetails = document.getElementById('showCompanyDetails').checked;

            // إعدادات الواتساب
            systemSettings.whatsappStyle = document.getElementById('whatsappStyle').value;
            systemSettings.whatsappHeader = document.getElementById('whatsappHeader').value;
            systemSettings.whatsappFooter = document.getElementById('whatsappFooter').value;
            systemSettings.showCompanyInfoInWhatsApp = document.getElementById('showCompanyInfoInWhatsApp').checked;
            systemSettings.showBordersInWhatsApp = document.getElementById('showBordersInWhatsApp').checked;

            // إعدادات إخفاء العناصر من الواتساب
            systemSettings.hideFromWhatsApp = [];
            if (document.getElementById('hidePrinterName').checked) {
                systemSettings.hideFromWhatsApp.push('printerName');
            }
            if (document.getElementById('hideJobDetails').checked) {
                systemSettings.hideFromWhatsApp.push('jobDetails');
            }
            if (document.getElementById('hideInvoiceNumber').checked) {
                systemSettings.hideFromWhatsApp.push('invoiceNumber');
            }

            localStorage.setItem('modernPrintingSettings', JSON.stringify(systemSettings));
            showMessage('✅ تم حفظ إعدادات التصميم بنجاح!', 'success');
            updateInvoicePreview();
            updateWhatsAppPreview();
        }

        // إعادة تعيين تصميم الفاتورة
        function resetInvoiceDesign() {
            if (confirm('هل أنت متأكد من إعادة تعيين تصميم الفاتورة؟')) {
                systemSettings.invoiceTheme = 'modern';
                systemSettings.invoiceColor = '#3b82f6';
                systemSettings.invoiceHeaderStyle = 'gradient';
                systemSettings.footerMessage = '🙏 شكراً لثقتكم بنا';
                systemSettings.showLogo = true;
                systemSettings.showBorder = true;
                systemSettings.showFooterMessage = true;
                systemSettings.showCompanyDetails = true;
                systemSettings.hideFromWhatsApp = ['printerName'];

                loadInvoiceDesignSettings();
                localStorage.setItem('modernPrintingSettings', JSON.stringify(systemSettings));
                showMessage('✅ تم إعادة تعيين تصميم الفاتورة!', 'success');
                updateInvoicePreview();
            }
        }

        // إعادة تعيين الإعدادات
        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
                localStorage.removeItem('modernPrintingSettings');
                systemSettings = {
                    companyName: 'مطبعة الحديثة',
                    companyPhone: '*********',
                    companyAddress: 'صنعاء - اليمن',
                    companyEmail: '<EMAIL>',
                    companyWebsite: 'www.modernprint.ye',
                    companyLogo: '',
                    defaultCurrency: 'ريال يمني',
                    dateFormat: 'en-US',
                    numberFormat: 'english'
                };
                loadSettingsToForm();
                showMessage('✅ تم إعادة تعيين الإعدادات بنجاح!', 'success');
            }
        }

        // تحميل الإعدادات في النموذج
        function loadSettingsToForm() {
            document.getElementById('companyName').value = systemSettings.companyName;
            document.getElementById('companyPhone').value = systemSettings.companyPhone;
            document.getElementById('companyAddress').value = systemSettings.companyAddress;
            document.getElementById('companyEmail').value = systemSettings.companyEmail;
            document.getElementById('companyWebsite').value = systemSettings.companyWebsite;
            document.getElementById('companyLogo').value = systemSettings.companyLogo;
            document.getElementById('defaultCurrency').value = systemSettings.defaultCurrency;
            document.getElementById('dateFormat').value = systemSettings.dateFormat;
            document.getElementById('numberFormat').value = systemSettings.numberFormat;

            // تحميل إعدادات التصميم
            loadInvoiceDesignSettings();
        }

        // تحميل إعدادات تصميم الفاتورة
        function loadInvoiceDesignSettings() {
            document.getElementById('invoiceTheme').value = systemSettings.invoiceTheme || 'modern';
            document.getElementById('invoiceColor').value = systemSettings.invoiceColor || '#3b82f6';
            document.getElementById('invoiceHeaderStyle').value = systemSettings.invoiceHeaderStyle || 'gradient';
            document.getElementById('footerMessage').value = systemSettings.footerMessage || '🙏 شكراً لثقتكم بنا';
            document.getElementById('showLogo').checked = systemSettings.showLogo !== false;
            document.getElementById('showBorder').checked = systemSettings.showBorder !== false;
            document.getElementById('showFooterMessage').checked = systemSettings.showFooterMessage !== false;
            document.getElementById('showCompanyDetails').checked = systemSettings.showCompanyDetails !== false;

            // تحميل إعدادات الواتساب
            document.getElementById('whatsappStyle').value = systemSettings.whatsappStyle || 'modern';
            document.getElementById('whatsappHeader').value = systemSettings.whatsappHeader || '🖨️ *فاتورة طباعة*';
            document.getElementById('whatsappFooter').value = systemSettings.whatsappFooter || '🙏 شكراً لثقتكم بنا';
            document.getElementById('showCompanyInfoInWhatsApp').checked = systemSettings.showCompanyInfoInWhatsApp !== false;
            document.getElementById('showBordersInWhatsApp').checked = systemSettings.showBordersInWhatsApp !== false;

            // تحميل إعدادات إخفاء العناصر
            const hideFromWhatsApp = systemSettings.hideFromWhatsApp || ['printerName'];
            document.getElementById('hidePrinterName').checked = hideFromWhatsApp.includes('printerName');
            document.getElementById('hideJobDetails').checked = hideFromWhatsApp.includes('jobDetails');
            document.getElementById('hideInvoiceNumber').checked = hideFromWhatsApp.includes('invoiceNumber');
        }

        // تحديث معاينة الفاتورة
        function updateInvoicePreview() {
            const sampleJob = {
                id: 1,
                name: 'طباعة بروشورات إعلانية',
                customerName: 'شركة الأمل للتجارة',
                invoiceNumber: 'INV-001',
                printedQuantity: '1000',
                unitPrice: 50,
                details: 'بروشورات ملونة عالية الجودة',
                printerName: 'أحمد محمد',
                createdAt: new Date().toISOString()
            };

            const previewContent = generateInvoiceContent(sampleJob);
            document.getElementById('invoicePreview').innerHTML = previewContent;
        }

        // إنشاء رسالة واتساب محسنة
        // دالة إنشاء رسالة واتساب محسنة
        function generateEnhancedWhatsAppMessage(job) {
            const companyName = systemSettings.companyName || 'مطبعة الجودة';
            const companyPhone = systemSettings.companyPhone || '';
            const companyLogo = systemSettings.companyLogo || '🖨️';

            // حساب المبلغ الإجمالي
            const totalAmount = calculateJobAmount(job);
            const formattedDate = new Date(job.createdAt).toLocaleDateString('en-GB');

            // إنشاء رسالة رسمية وبسيطة
            const message = `
*${companyName}*
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

*فاتورة طباعة*

*رقم الفاتورة:* ${convertToEnglishNumbers(job.invoiceNumber || `#${job.id}`)}
*اسم العميل:* ${job.customerName || 'غير محدد'}
*نوع العمل:* ${job.name}
*الكمية:* ${convertToEnglishNumbers(job.totalQuantity || job.printedQuantity || 'غير محدد')}
*سعر الحبة:* ${convertToEnglishNumbers(formatCurrency(job.unitPrice || 0))}
*الطباع:* ${job.printerName || 'غير محدد'}
*التاريخ:* ${formattedDate}

${job.details ? `*ملاحظات:* ${job.details}\n` : ''}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
*المبلغ الإجمالي:* ${convertToEnglishNumbers(formatCurrency(totalAmount))}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

${companyPhone ? `*الهاتف:* ${companyPhone}` : ''}

شكراً لتعاملكم معنا
            `.trim();

            return message;
        }

        // دالة إنشاء صورة للفاتورة
        async function generateInvoiceImage(job) {
            return new Promise((resolve, reject) => {
                try {
                    // إنشاء canvas لرسم الفاتورة
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    // تحديد أبعاد الكانفاس
                    canvas.width = 800;
                    canvas.height = 1000;
                    
                    // خلفية بيضاء
                    ctx.fillStyle = '#ffffff';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // إعدادات الخط
                    ctx.textAlign = 'right';
                    ctx.direction = 'rtl';
                    
                    let y = 50;
                    const rightMargin = canvas.width - 50;
                    const leftMargin = 50;
                    
                    // رسم الهيدر
                    ctx.fillStyle = '#1e293b';
                    ctx.fillRect(0, 0, canvas.width, 120);
                    
                    // اسم الشركة
                    ctx.fillStyle = '#ffffff';
                    ctx.font = 'bold 32px Arial';
                    ctx.fillText(`${systemSettings.companyLogo || '🖨️'} ${systemSettings.companyName || 'مطبعة الجودة'}`, rightMargin, 45);
                    
                    // عنوان الفاتورة
                    ctx.font = 'bold 24px Arial';
                    ctx.fillText('فاتورة طباعة', rightMargin, 85);
                    
                    y = 160;
                    
                    // معلومات الفاتورة
                    ctx.fillStyle = '#1e293b';
                    ctx.font = 'bold 20px Arial';
                    
                    const invoiceData = [
                        [`رقم الفاتورة: ${job.invoiceNumber || `#${job.id}`}`, '🆔'],
                        [`العميل: ${job.customerName || 'غير محدد'}`, '👤'],
                        [`اسم العمل: ${job.name}`, '📝'],
                        [`الكمية: ${job.totalQuantity || job.printedQuantity || 'غير محدد'}`, '📊'],
                        [`الطباع: ${job.printerName || 'غير محدد'}`, '🖨️'],
                        [`التاريخ: ${convertToEnglishNumbers(new Date(job.createdAt).toLocaleDateString('ar-SA'))}`, '📅'],
                        [`الوقت: ${new Date(job.createdAt).toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}`, '⏰']
                    ];
                    
                    // رسم خط فاصل
                    ctx.strokeStyle = '#e2e8f0';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(leftMargin, y);
                    ctx.lineTo(rightMargin, y);
                    ctx.stroke();
                    
                    y += 30;
                    
                    // رسم بيانات الفاتورة
                    invoiceData.forEach(([text, icon]) => {
                        // رسم خلفية للصف
                        ctx.fillStyle = y % 80 === 0 ? '#f8fafc' : '#ffffff';
                        ctx.fillRect(leftMargin, y - 25, canvas.width - 100, 35);
                        
                        // رسم الأيقونة
                        ctx.fillStyle = '#3b82f6';
                        ctx.font = '20px Arial';
                        ctx.fillText(icon, rightMargin - 20, y);
                        
                        // رسم النص
                        ctx.fillStyle = '#1e293b';
                        ctx.font = '18px Arial';
                        ctx.fillText(text, rightMargin - 50, y);
                        
                        y += 40;
                    });
                    
                    // الملاحظات
                    if (job.details) {
                        y += 20;
                        ctx.fillStyle = '#64748b';
                        ctx.font = 'bold 18px Arial';
                        ctx.fillText('📋 ملاحظات:', rightMargin, y);
                        
                        y += 30;
                        ctx.fillStyle = '#374151';
                        ctx.font = '16px Arial';
                        
                        // تقسيم النص إلى أسطر
                        const words = job.details.split(' ');
                        let line = '';
                        const maxWidth = canvas.width - 100;
                        
                        for (let word of words) {
                            const testLine = line + word + ' ';
                            const metrics = ctx.measureText(testLine);
                            
                            if (metrics.width > maxWidth && line !== '') {
                                ctx.fillText(line, rightMargin, y);
                                line = word + ' ';
                                y += 25;
                            } else {
                                line = testLine;
                            }
                        }
                        ctx.fillText(line, rightMargin, y);
                        y += 30;
                    }
                    
                    // المبلغ الإجمالي
                    y += 30;
                    ctx.fillStyle = '#10b981';
                    ctx.fillRect(leftMargin, y - 25, canvas.width - 100, 50);
                    
                    ctx.fillStyle = '#ffffff';
                    ctx.font = 'bold 24px Arial';
                    ctx.fillText(`💰 المبلغ الإجمالي: ${formatCurrency(calculateJobAmount(job))}`, rightMargin - 20, y + 5);
                    
                    // الفوتر
                    y += 80;
                    ctx.fillStyle = '#64748b';
                    ctx.font = '16px Arial';
                    if (systemSettings.companyPhone) {
                        ctx.fillText(`📱 ${systemSettings.companyPhone}`, rightMargin, y);
                        y += 25;
                    }
                    ctx.fillText('🙏 شكراً لثقتكم بنا', rightMargin, y);
                    
                    y += 30;
                    ctx.fillStyle = '#9ca3af';
                    ctx.font = '14px Arial';
                    ctx.fillText('تم إنشاء هذه الفاتورة تلقائياً', rightMargin, y);
                    
                    // تحويل الكانفاس إلى blob
                    canvas.toBlob((blob) => {
                        if (blob) {
                            resolve(blob);
                        } else {
                            reject(new Error('فشل في إنشاء الصورة'));
                        }
                    }, 'image/png', 0.9);
                    
                } catch (error) {
                    reject(error);
                }
            });
        }

        // دالة إنشاء PDF للفاتورة
        async function generateInvoicePDF(job) {
            return new Promise((resolve, reject) => {
                try {
                    // إنشاء محتوى HTML للفاتورة
                    const invoiceHTML = generateInvoiceHTML(job);
                    
                    // إنشاء نافذة جديدة للطباعة
                    const printWindow = window.open('', '_blank');
                    printWindow.document.write(invoiceHTML);
                    printWindow.document.close();
                    
                    // انتظار تحميل المحتوى ثم الطباعة
                    printWindow.onload = () => {
                        setTimeout(() => {
                            printWindow.print();
                            
                            // إنشاء رابط تحميل PDF (محاكاة)
                            const pdfBlob = new Blob([invoiceHTML], { type: 'application/pdf' });
                            const url = URL.createObjectURL(pdfBlob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `فاتورة_${job.invoiceNumber || job.id}.pdf`;
                            a.click();
                            URL.revokeObjectURL(url);
                            
                            printWindow.close();
                            resolve();
                        }, 1000);
                    };
                    
                } catch (error) {
                    reject(error);
                }
            });
        }

        // دالة إنشاء HTML للفاتورة
        function generateInvoiceHTML(job) {
            const totalAmount = calculateJobAmount(job);
            const formattedDate = convertToEnglishNumbers(new Date(job.createdAt).toLocaleDateString('ar-SA'));
            const formattedTime = new Date(job.createdAt).toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
            
            return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة طباعة - ${job.invoiceNumber || job.id}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Arial', sans-serif; 
            direction: rtl; 
            background: white;
            color: #1e293b;
            line-height: 1.6;
        }
        .invoice-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 30px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #1e293b 0%, #**********%);
            color: white;
            padding: 30px;
            text-align: center;
            margin: -30px -30px 30px -30px;
            border-radius: 0;
        }
        .company-name { font-size: 28px; font-weight: bold; margin-bottom: 10px; }
        .invoice-title { font-size: 20px; opacity: 0.9; }
        .invoice-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-section {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-right: 4px solid #3b82f6;
        }
        .info-title { font-weight: bold; color: #1e293b; margin-bottom: 15px; font-size: 18px; }
        .info-item { margin-bottom: 8px; display: flex; align-items: center; }
        .info-icon { margin-left: 8px; font-size: 16px; }
        .details-section {
            background: #f0fdf4;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-right: 4px solid #10b981;
        }
        .total-section {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 25px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .total-amount { font-size: 24px; font-weight: bold; }
        .footer {
            text-align: center;
            color: #64748b;
            border-top: 2px solid #e2e8f0;
            padding-top: 20px;
        }
        @media print {
            body { background: white; }
            .invoice-container { box-shadow: none; margin: 0; }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="header">
            <div class="company-name">${systemSettings.companyLogo || '🖨️'} ${systemSettings.companyName || 'مطبعة الجودة'}</div>
            <div class="invoice-title">فاتورة طباعة</div>
        </div>
        
        <div class="invoice-info">
            <div class="info-section">
                <div class="info-title">معلومات الفاتورة</div>
                <div class="info-item"><span class="info-icon">🆔</span> رقم الفاتورة: ${job.invoiceNumber || `#${job.id}`}</div>
                <div class="info-item"><span class="info-icon">📅</span> التاريخ: ${formattedDate}</div>
                <div class="info-item"><span class="info-icon">⏰</span> الوقت: ${formattedTime}</div>
            </div>
            
            <div class="info-section">
                <div class="info-title">معلومات العمل</div>
                <div class="info-item"><span class="info-icon">👤</span> العميل: ${job.customerName || 'غير محدد'}</div>
                <div class="info-item"><span class="info-icon">📝</span> اسم العمل: ${job.name}</div>
                <div class="info-item"><span class="info-icon">📊</span> الكمية: ${job.totalQuantity || job.printedQuantity || 'غير محدد'}</div>
                <div class="info-item"><span class="info-icon">🖨️</span> الطباع: ${job.printerName || 'غير محدد'}</div>
            </div>
        </div>
        
        ${job.details ? `
        <div class="details-section">
            <div class="info-title">📋 ملاحظات</div>
            <p>${job.details}</p>
        </div>
        ` : ''}
        
        <div class="total-section">
            <div class="total-amount">💰 المبلغ الإجمالي: ${formatCurrency(totalAmount)}</div>
        </div>
        
        <div class="footer">
            ${systemSettings.companyPhone ? `<p>📱 ${systemSettings.companyPhone}</p>` : ''}
            <p>🙏 شكراً لثقتكم بنا</p>
            <p style="font-size: 12px; margin-top: 10px; opacity: 0.7;">تم إنشاء هذه الفاتورة تلقائياً</p>
        </div>
    </div>
</body>
</html>
            `;
        }

        function generateWhatsAppMessage(job) {
            const style = systemSettings.whatsappStyle || 'modern';
            const header = systemSettings.whatsappHeader || '🖨️ *فاتورة طباعة*';
            const footer = systemSettings.whatsappFooter || '🙏 شكراً لثقتكم بنا';
            const showCompanyInfo = systemSettings.showCompanyInfoInWhatsApp !== false;
            const showBorders = systemSettings.showBordersInWhatsApp !== false;
            const hideFromWhatsApp = systemSettings.hideFromWhatsApp || [];

            let message = '';
            const border = showBorders ? '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━' : '';
            const shortBorder = showBorders ? '─────────────────────────────' : '';

            switch (style) {
                case 'business':
                    message = `
${systemSettings.companyLogo} *${systemSettings.companyName}*

${header}
${border}

📋 تفاصيل الفاتورة:
${!hideFromWhatsApp.includes('invoiceNumber') ? `• رقم الفاتورة: ${job.invoiceNumber || 'غير محدد'}` : ''}
• العميل: ${job.customerName || 'غير محدد'}
• اسم العمل: ${job.name}
• الكمية: ${job.totalQuantity || job.printedQuantity || 'غير محدد'}
${!hideFromWhatsApp.includes('printerName') ? `• الطباع: ${job.printerName || 'غير محدد'}` : ''}
• المبلغ: ${formatCurrency(calculateJobAmount(job))}
• التاريخ: ${convertToEnglishNumbers(new Date(job.createdAt).toLocaleDateString('en-US'))}

${!hideFromWhatsApp.includes('jobDetails') && job.details ? `📋 ملاحظات: ${job.details}` : ''}

${border}
${showCompanyInfo ? `📱 ${systemSettings.companyPhone} | 🏢 ${systemSettings.companyName}` : ''}
${footer}`;
                    break;

                case 'classic':
                    message = `
${header}

العميل: ${job.customerName || 'غير محدد'}
العمل: ${job.name}
${!hideFromWhatsApp.includes('invoiceNumber') ? `رقم الفاتورة: ${job.invoiceNumber || 'غير محدد'}` : ''}
الكمية: ${job.totalQuantity || job.printedQuantity || 'غير محدد'}
${!hideFromWhatsApp.includes('printerName') ? `الطباع: ${job.printerName || 'غير محدد'}` : ''}
المبلغ: ${formatCurrency(calculateJobAmount(job))}
التاريخ: ${convertToEnglishNumbers(new Date(job.createdAt).toLocaleDateString('en-US'))}

${!hideFromWhatsApp.includes('jobDetails') && job.details ? `التفاصيل: ${job.details}` : ''}

${showCompanyInfo ? `${systemSettings.companyName} - ${systemSettings.companyPhone}` : ''}
${footer}`;
                    break;

                case 'elegant':
                    message = `
╔══════════════════════════════╗
║      ${systemSettings.companyLogo} *${systemSettings.companyName}*      ║
╚══════════════════════════════╝

${header}
${border}

┌─────────────────────────────┐
│ 👤 العميل: ${job.customerName || 'غير محدد'}
${!hideFromWhatsApp.includes('invoiceNumber') ? `│ 📋 رقم الفاتورة: ${job.invoiceNumber || 'غير محدد'}` : ''}
│ 📝 العمل: ${job.name}
│ 📊 الكمية: ${job.totalQuantity || job.printedQuantity || 'غير محدد'}
${!hideFromWhatsApp.includes('printerName') ? `│ 🖨️ الطباع: ${job.printerName || 'غير محدد'}` : ''}
│ 💰 المبلغ: ${formatCurrency(calculateJobAmount(job))}
│ 📅 التاريخ: ${convertToEnglishNumbers(new Date(job.createdAt).toLocaleDateString('en-US'))}
└─────────────────────────────┘

${!hideFromWhatsApp.includes('jobDetails') && job.details ? `📋 *التفاصيل:* ${job.details}` : ''}

${border}
${showCompanyInfo ? `📱 ${systemSettings.companyPhone} | 🏢 ${systemSettings.companyName}` : ''}
${footer}`;
                    break;

                default: // modern
                    message = `
${systemSettings.companyLogo} *${systemSettings.companyName}*

${header}
${border}

${!hideFromWhatsApp.includes('invoiceNumber') ? `📋 *رقم الفاتورة:* ${job.invoiceNumber || 'غير محدد'}` : ''}
👤 *العميل:* ${job.customerName || 'غير محدد'}
📝 *اسم العمل:* ${job.name}
📊 *الكمية:* ${job.totalQuantity || job.printedQuantity || 'غير محدد'}
${!hideFromWhatsApp.includes('printerName') ? `🖨️ *الطباع:* ${job.printerName || 'غير محدد'}` : ''}
💰 *المبلغ:* ${formatCurrency(calculateJobAmount(job))}
📅 *التاريخ:* ${convertToEnglishNumbers(new Date(job.createdAt).toLocaleDateString('en-US'))}

${!hideFromWhatsApp.includes('jobDetails') && job.details ? `📋 *التفاصيل:* ${job.details}` : ''}

${border}
${showCompanyInfo ? `📱 ${systemSettings.companyPhone} | 🏢 ${systemSettings.companyName}` : ''}
${footer}`;
            }

            // إزالة الأسطر الفارغة الزائدة
            return message.trim().replace(/\n\s*\n\s*\n/g, '\n\n');
        }

        // تحديث معاينة رسالة الواتساب
        function updateWhatsAppPreview() {
            const sampleJob = {
                id: 1,
                name: 'طباعة بروشورات إعلانية',
                customerName: 'شركة الأمل للتجارة',
                invoiceNumber: 'INV-001',
                printedQuantity: '1000',
                totalQuantity: '1000',
                unitPrice: 50,
                details: 'بروشورات ملونة عالية الجودة',
                printerName: 'أحمد محمد',
                createdAt: new Date().toISOString()
            };

            const message = generateWhatsAppMessage(sampleJob);
            document.getElementById('whatsappPreview').textContent = message;
        }

        // معاينة رسالة الواتساب (دالة للزر)
        function previewWhatsAppMessage() {
            updateWhatsAppPreview();
            // التمرير إلى قسم المعاينة
            document.getElementById('whatsappPreview').scrollIntoView({ behavior: 'smooth' });
        }

        // اختبار إرسال رسالة الواتساب
        function testWhatsAppMessage() {
            const sampleJob = {
                id: 1,
                name: 'طباعة بروشورات إعلانية',
                customerName: 'شركة الأمل للتجارة',
                invoiceNumber: 'INV-001',
                printedQuantity: '1000',
                totalQuantity: '1000',
                unitPrice: 50,
                details: 'بروشورات ملونة عالية الجودة',
                printerName: 'أحمد محمد',
                createdAt: new Date().toISOString()
            };

            const message = generateWhatsAppMessage(sampleJob);
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
            showMessage('✅ تم فتح واتساب مع رسالة تجريبية', 'success');
        }

        // متغير لتخزين الأعمال الإضافية
        let additionalJobs = [];

        // فحص وجود فواتير مكررة
        function checkForDuplicateInvoice() {
            const invoiceNumber = document.getElementById('invoiceNumber').value;
            const existingJobs = jobs.filter(job => job.invoiceNumber === invoiceNumber);

            const container = document.getElementById('additionalJobsContainer');

            if (existingJobs.length > 0 && invoiceNumber.trim() !== '') {
                container.style.display = 'block';
                showMessage(`⚠️ تم العثور على ${existingJobs.length} عمل بنفس رقم الفاتورة`, 'warning');
            } else {
                container.style.display = 'none';
                additionalJobs = [];
                updateAdditionalJobsList();
            }
        }

        // إضافة عمل إضافي
        function addAdditionalJob() {
            const newJob = {
                id: Date.now() + Math.random(),
                name: '',
                details: ''
            };

            additionalJobs.push(newJob);
            updateAdditionalJobsList();
        }

        // تحديث قائمة الأعمال الإضافية
        function updateAdditionalJobsList() {
            const container = document.getElementById('additionalJobsList');

            container.innerHTML = additionalJobs.map((job, index) => `
                <div style="
                    background: white;
                    border: 2px solid #e5e7eb;
                    border-radius: 8px;
                    padding: 15px;
                    margin-bottom: 10px;
                ">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <h5 style="margin: 0; color: #1e293b;">📝 عمل إضافي ${index + 1}</h5>
                        <button type="button" onclick="removeAdditionalJob(${index})" style="
                            background: #ef4444;
                            color: white;
                            border: none;
                            border-radius: 50%;
                            width: 25px;
                            height: 25px;
                            cursor: pointer;
                            font-size: 12px;
                        ">×</button>
                    </div>

                    <div style="margin-bottom: 10px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151; font-size: 12px;">اسم العمل:</label>
                        <input type="text"
                            value="${job.name}"
                            placeholder="اسم العمل الإضافي"
                            style="
                                width: 100%;
                                padding: 8px 12px;
                                border: 1px solid #d1d5db;
                                border-radius: 6px;
                                font-size: 14px;
                            "
                            onchange="updateAdditionalJob(${index}, 'name', this.value)">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151; font-size: 12px;">تفاصيل العمل:</label>
                        <textarea
                            placeholder="تفاصيل العمل الإضافي"
                            style="
                                width: 100%;
                                padding: 8px 12px;
                                border: 1px solid #d1d5db;
                                border-radius: 6px;
                                font-size: 14px;
                                min-height: 60px;
                                resize: vertical;
                            "
                            onchange="updateAdditionalJob(${index}, 'details', this.value)">${job.details}</textarea>
                    </div>
                </div>
            `).join('');
        }

        // تحديث عمل إضافي
        function updateAdditionalJob(index, field, value) {
            if (additionalJobs[index]) {
                additionalJobs[index][field] = value;
            }
        }

        // حذف عمل إضافي
        function removeAdditionalJob(index) {
            additionalJobs.splice(index, 1);
            updateAdditionalJobsList();
        }

        // تحديث تنسيق العملة حسب الإعدادات
        function formatCurrency(amount) {
            const formatted = (amount || 0).toLocaleString(systemSettings.numberFormat === 'english' ? 'en-US' : 'ar-EG');
            const currency = systemSettings.defaultCurrency || 'ريال يمني';

            if (systemSettings.numberFormat === 'english') {
                return convertToEnglishNumbers(`${formatted} ${currency}`);
            }
            return `${formatted} ${currency}`;
        }

        console.log('نظام إدارة الطباعة الحديث جاهز');
    </script>

    <!-- مكتبات لتحسين إنشاء الصور وملفات PDF -->
    <script src="./الجديد  نظام إدارة الطباعة_files/html2canvas.min.js.تنزيل"></script>
    <script src="./الجديد  نظام إدارة الطباعة_files/jspdf.umd.min.js.تنزيل"></script>
    <script src="./الجديد  نظام إدارة الطباعة_files/html2pdf.bundle.min(1).js.تنزيل"></script>
    
    <script>
        // تحسين دالة إنشاء الصورة باستخدام html2canvas إذا كانت متوفرة
        async function generateInvoiceImageWithCanvas(job) {
            if (typeof html2canvas === 'undefined') {
                // استخدام الطريقة الأصلية إذا لم تكن المكتبة متوفرة
                return generateInvoiceImage(job);
            }

            return new Promise((resolve, reject) => {
                try {
                    // إنشاء عنصر HTML للفاتورة
                    const invoiceElement = document.createElement('div');
                    invoiceElement.innerHTML = generateInvoiceHTML(job);
                    invoiceElement.style.position = 'absolute';
                    invoiceElement.style.left = '-9999px';
                    invoiceElement.style.top = '-9999px';
                    invoiceElement.style.width = '800px';
                    invoiceElement.style.background = 'white';
                    
                    document.body.appendChild(invoiceElement);

                    // استخدام html2canvas لتحويل العنصر إلى صورة
                    html2canvas(invoiceElement, {
                        width: 800,
                        height: 1000,
                        scale: 2,
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: '#ffffff'
                    }).then(canvas => {
                        // تحويل الكانفاس إلى blob
                        canvas.toBlob((blob) => {
                            document.body.removeChild(invoiceElement);
                            if (blob) {
                                resolve(blob);
                            } else {
                                reject(new Error('فشل في إنشاء الصورة'));
                            }
                        }, 'image/png', 0.9);
                    }).catch(error => {
                        document.body.removeChild(invoiceElement);
                        reject(error);
                    });

                } catch (error) {
                    reject(error);
                }
            });
        }

        // تحديث دالة مشاركة الصورة لاستخدام المكتبة المحسنة
        function shareAsImageEnhanced(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            // استخدام المكتبة المحسنة إذا كانت متوفرة
            const imageGenerator = typeof html2canvas !== 'undefined' ? 
                generateInvoiceImageWithCanvas : generateInvoiceImage;

            imageGenerator(job).then(imageBlob => {
                // إنشاء رابط تحميل مؤقت
                const url = URL.createObjectURL(imageBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `فاتورة_${job.invoiceNumber || job.id}.png`;
                a.click();
                URL.revokeObjectURL(url);

                // فتح واتساب مع رسالة مرفقة
                const message = `🖨️ فاتورة طباعة\n\n👤 العميل: ${job.customerName}\n📝 العمل: ${job.name}\n💰 المبلغ: ${formatCurrency(calculateJobAmount(job))}\n\n📎 الفاتورة مرفقة كصورة عالية الجودة`;
                const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
                window.open(whatsappUrl, '_blank');

                document.querySelector('.modal-overlay').remove();
                hideShareOptions(jobId);
                showMessage('✅ تم تحميل صورة الفاتورة بجودة عالية وفتح واتساب', 'success');
            }).catch(error => {
                console.error('خطأ في إنشاء الصورة:', error);
                showMessage('❌ حدث خطأ في إنشاء صورة الفاتورة', 'error');
            });
        }

        // إظهار أزرار المشاركة والـ PDF بعد حفظ اسم العميل
        function showShareButtons(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job || !job.customerName) return;

            // البحث عن منطقة الحفظ في البطاقة
            const saveButton = document.querySelector(`button[onclick="saveQuickUpdate(${jobId})"]`);
            if (!saveButton) return;

            // إزالة أزرار المشاركة السابقة إن وجدت
            const existingShareDiv = document.getElementById(`share_buttons_${jobId}`);
            if (existingShareDiv) {
                existingShareDiv.remove();
            }

            // إنشاء منطقة أزرار المشاركة
            const shareDiv = document.createElement('div');
            shareDiv.id = `share_buttons_${jobId}`;
            shareDiv.style.cssText = `
                margin-top: 15px;
                padding: 15px;
                background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
                border: 2px solid #0ea5e9;
                border-radius: 12px;
                text-align: center;
                animation: slideDown 0.3s ease;
            `;

            shareDiv.innerHTML = `
                <div style="margin-bottom: 10px; font-weight: bold; color: #0c4a6e;">
                    🎉 تم حفظ بيانات العميل بنجاح!
                </div>
                <div style="display: flex; gap: 8px; justify-content: center;">
                    <button onclick="shareViaWhatsApp(${jobId})" style="
                        flex: 1;
                        padding: 10px 12px;
                        background: linear-gradient(135deg, #10b981, #059669);
                        color: white;
                        border: none;
                        border-radius: 8px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        font-size: 14px;
                    " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                        📱 مشاركة واتساب
                    </button>
                    <button onclick="downloadAsPDF(${jobId})" style="
                        flex: 1;
                        padding: 10px 12px;
                        background: linear-gradient(135deg, #ef4444, #dc2626);
                        color: white;
                        border: none;
                        border-radius: 8px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        font-size: 14px;
                    " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                        📄 تحميل PDF
                    </button>
                </div>
            `;

            // إضافة CSS للأنيميشن
            if (!document.getElementById('shareButtonsCSS')) {
                const style = document.createElement('style');
                style.id = 'shareButtonsCSS';
                style.textContent = `
                    @keyframes slideDown {
                        from {
                            opacity: 0;
                            transform: translateY(-20px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }
                `;
                document.head.appendChild(style);
            }

            // إدراج أزرار المشاركة بعد زر الحفظ
            saveButton.parentNode.insertBefore(shareDiv, saveButton.nextSibling);
        }

        // مشاركة عبر واتساب
        function shareViaWhatsApp(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            const message = `
*${systemSettings.companyName || 'مطبعة الجودة'}*
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

*فاتورة طباعة*

*رقم الفاتورة:* ${convertToEnglishNumbers(job.invoiceNumber || 'غير محدد')}
*اسم العميل:* ${job.customerName}
*نوع العمل:* ${job.name}
*الكمية:* ${convertToEnglishNumbers(job.printedQuantity || 'غير محدد')}
*سعر الحبة:* ${convertToEnglishNumbers(formatCurrency(job.unitPrice || 0))}
*التاريخ:* ${new Date(job.createdAt).toLocaleDateString('en-GB')}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
*المبلغ الإجمالي:* ${convertToEnglishNumbers(formatCurrency(calculateJobAmount(job)))}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

شكراً لتعاملكم معنا
            `.trim();

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showMessage('✅ تم فتح واتساب لمشاركة تفاصيل الفاتورة', 'success');
        }

        // تحميل كـ PDF
        function downloadAsPDF(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            // إنشاء محتوى PDF محسن
            const pdfContent = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>فاتورة ${job.customerName} - ${job.invoiceNumber}</title>
                    <style>
                        @page { margin: 20mm; }
                        body {
                            font-family: 'Arial', 'Tahoma', sans-serif;
                            direction: rtl;
                            margin: 0;
                            padding: 20px;
                            line-height: 1.6;
                            color: #333;
                            background: #fff;
                        }

                        .invoice-container {
                            max-width: 800px;
                            margin: 0 auto;
                            border: 3px solid #0ea5e9;
                            border-radius: 15px;
                            overflow: hidden;
                            box-shadow: 0 0 20px rgba(0,0,0,0.1);
                        }

                        .header {
                            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
                            color: white;
                            text-align: center;
                            padding: 30px 20px;
                            margin: 0;
                        }

                        .header h1 {
                            margin: 0 0 10px 0;
                            font-size: 32px;
                            font-weight: bold;
                            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                        }

                        .header h2 {
                            margin: 0;
                            font-size: 20px;
                            font-weight: normal;
                            opacity: 0.9;
                        }

                        .company-info {
                            background: #f8fafc;
                            padding: 20px;
                            text-align: center;
                            border-bottom: 2px solid #e2e8f0;
                        }

                        .company-name {
                            font-size: 24px;
                            font-weight: bold;
                            color: #1e293b;
                            margin-bottom: 5px;
                        }

                        .invoice-details {
                            padding: 30px;
                            background: white;
                        }

                        .details-table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 20px 0;
                            font-size: 16px;
                        }

                        .details-table tr {
                            border-bottom: 1px solid #e2e8f0;
                        }

                        .details-table tr:nth-child(even) {
                            background: #f8fafc;
                        }

                        .details-table td {
                            padding: 15px 20px;
                            vertical-align: middle;
                        }

                        .details-table .label {
                            font-weight: bold;
                            color: #475569;
                            width: 40%;
                            background: #f1f5f9;
                        }

                        .details-table .value {
                            color: #1e293b;
                            font-weight: 500;
                        }

                        .calculation-section {
                            background: #f8fafc;
                            padding: 25px;
                            margin: 20px 0;
                            border-radius: 10px;
                            border: 2px solid #e2e8f0;
                        }

                        .calculation-row {
                            display: flex;
                            justify-content: space-between;
                            padding: 10px 0;
                            font-size: 18px;
                        }

                        .calculation-row.total {
                            border-top: 3px solid #0ea5e9;
                            margin-top: 15px;
                            padding-top: 15px;
                            font-size: 24px;
                            font-weight: bold;
                            color: #0ea5e9;
                        }

                        .footer {
                            background: #1e293b;
                            color: white;
                            text-align: center;
                            padding: 25px;
                            margin: 0;
                        }

                        .footer p {
                            margin: 5px 0;
                            font-size: 14px;
                        }

                        .thank-you {
                            font-size: 18px;
                            font-weight: bold;
                            margin-bottom: 10px;
                        }

                        .print-date {
                            opacity: 0.8;
                            font-size: 12px;
                        }
                    </style>
                </head>
                <body>
                    <div class="invoice-container">
                        <div class="header">
                            <h1>🖨️ فاتورة طباعة</h1>
                            <h2>رقم الفاتورة: ${convertToEnglishNumbers(job.invoiceNumber || 'غير محدد')}</h2>
                        </div>

                        <div class="company-info">
                            <div class="company-name">${systemSettings.companyName || 'مطبعة الجودة'}</div>
                            ${systemSettings.companyPhone ? `<div>📞 ${systemSettings.companyPhone}</div>` : ''}
                        </div>

                        <div class="invoice-details">
                            <table class="details-table">
                                <tr>
                                    <td class="label">اسم العميل:</td>
                                    <td class="value">${job.customerName}</td>
                                </tr>
                                <tr>
                                    <td class="label">نوع العمل:</td>
                                    <td class="value">${job.name}</td>
                                </tr>
                                <tr>
                                    <td class="label">الكمية المطبوعة:</td>
                                    <td class="value">${convertToEnglishNumbers(job.printedQuantity || 'غير محدد')}</td>
                                </tr>
                                <tr>
                                    <td class="label">سعر الحبة:</td>
                                    <td class="value">${convertToEnglishNumbers(formatCurrency(job.unitPrice || 0))}</td>
                                </tr>
                                <tr>
                                    <td class="label">الطباع المسؤول:</td>
                                    <td class="value">${job.printerName || 'غير محدد'}</td>
                                </tr>
                                <tr>
                                    <td class="label">تاريخ الإنجاز:</td>
                                    <td class="value">${new Date(job.createdAt).toLocaleDateString('en-GB')}</td>
                                </tr>
                            </table>

                            <div class="calculation-section">
                                <div class="calculation-row">
                                    <span>الكمية:</span>
                                    <span>${convertToEnglishNumbers(job.printedQuantity || 0)}</span>
                                </div>
                                <div class="calculation-row">
                                    <span>سعر الحبة:</span>
                                    <span>${convertToEnglishNumbers(formatCurrency(job.unitPrice || 0))}</span>
                                </div>
                                <div class="calculation-row total">
                                    <span>💰 المبلغ الإجمالي:</span>
                                    <span>${convertToEnglishNumbers(formatCurrency(calculateJobAmount(job)))}</span>
                                </div>
                            </div>
                        </div>

                        <div class="footer">
                            <p class="thank-you">🙏 شكراً لتعاملكم معنا</p>
                            <p>نتطلع لخدمتكم مرة أخرى</p>
                            <p class="print-date">تم إنشاء هذه الفاتورة بتاريخ: ${new Date().toLocaleDateString('en-GB')}</p>
                        </div>
                    </div>
                </body>
                </html>
            `;

            // إنشاء ملف PDF باستخدام window.print
            const printWindow = window.open('', '_blank');
            printWindow.document.write(pdfContent);
            printWindow.document.close();

            // تسمية الملف باسم العميل ورقم الفاتورة
            printWindow.document.title = `فاتورة_${job.customerName}_${job.invoiceNumber || job.id}`;

            setTimeout(() => {
                printWindow.print();
                showMessage('✅ تم فتح نافذة الطباعة - يمكنك حفظ الملف كـ PDF', 'success');
            }, 500);
        }

        // تحديث دالة shareAsImage لاستخدام النسخة المحسنة
        window.shareAsImage = shareAsImageEnhanced;
    </script>

</body></html>