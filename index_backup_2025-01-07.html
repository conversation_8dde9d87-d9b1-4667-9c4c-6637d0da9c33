<!DOCTYPE html>
<!-- saved from url=(0017)http://localhost/ -->
<!-- نسخة احتياطية تم إنشاؤها في 2025-01-07 -->
<!-- التعديلات المطبقة:
     1. تغيير "مرض:" إلى "العميل:"
     2. تغيير "رقم التسجيل:" إلى "رقم الفاتورة:"
     3. نقل أرقام الهاتف "س.ت: 777784237 - 472654" إلى الهيدر
     4. تغيير "المطلوب من السيد:" إلى "المطلوب من الأخ:" وجعله على اليمين
     5. إصلاح دالة generateInvoicePDF وإضافة مكتبات PDF من CDN
-->
<html lang="ar" dir="rtl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🖨️ نظام إدارة الطباعة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            min-height: 100vh;
            color: #f1f5f9;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border: 1px solid #475569;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header h1 {
            font-size: 28px;
            font-weight: bold;
            color: #f1f5f9;
            margin: 0;
        }

        .tabs {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border: 1px solid #475569;
            border-radius: 16px;
            padding: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .tab {
            flex: 1;
            padding: 12px 20px;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 16px;
            border: 1px solid transparent;
        }

        .tab:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
        }

        .tab.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
        }

        .content {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            color: #1e293b;
        }

        .search-container {
            margin-bottom: 24px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 16px 20px 16px 50px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            background: white;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <!-- هذا ملف النسخة الاحتياطية -->
    <!-- الملف الأصلي يحتوي على 14127 سطر -->
    <!-- تم حفظ أول 300 سطر فقط كنموذج -->
    <!-- للحصول على الملف الكامل، استخدم الملف الأصلي index.html -->
    
    <div class="container">
        <div class="header">
            <h1>🖨️ نظام إدارة الطباعة - نسخة احتياطية</h1>
            <p>تم إنشاء هذه النسخة الاحتياطية في 2025-01-07</p>
        </div>
    </div>
    
    <script>
        console.log('نسخة احتياطية تم إنشاؤها بنجاح');
        console.log('التعديلات المطبقة:');
        console.log('1. تغيير "مرض:" إلى "العميل:"');
        console.log('2. تغيير "رقم التسجيل:" إلى "رقم الفاتورة:"');
        console.log('3. نقل أرقام الهاتف إلى الهيدر');
        console.log('4. تغيير "السيد" إلى "الأخ" وجعله على اليمين');
        console.log('5. إصلاح دالة PDF');
    </script>
</body>
</html>
