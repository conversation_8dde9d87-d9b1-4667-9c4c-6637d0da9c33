<?php
require_once '../config.php';

// التحقق من API Key
validateApiKey();

// الحصول على طريقة الطلب
$method = $_SERVER['REQUEST_METHOD'];

// معالجة طلبات OPTIONS للـ CORS
if ($method === 'OPTIONS') {
    sendResponse(['message' => 'OK']);
}

try {
    $pdo = getDBConnection();
    if (!$pdo) {
        sendError('فشل الاتصال بقاعدة البيانات', 500);
    }

    switch ($method) {
        case 'GET':
            handleGetCustomers($pdo);
            break;
        case 'POST':
            handleCreateCustomer($pdo);
            break;
        case 'PUT':
            handleUpdateCustomer($pdo);
            break;
        case 'DELETE':
            handleDeleteCustomer($pdo);
            break;
        default:
            sendError('طريقة الطلب غير مدعومة', 405);
    }
} catch (Exception $e) {
    error_log("Customers API Error: " . $e->getMessage());
    sendError('خطأ في الخادم', 500);
}

// دالة جلب العملاء
function handleGetCustomers($pdo) {
    $id = $_GET['id'] ?? null;
    $type = $_GET['type'] ?? null; // customer أو supplier
    $search = $_GET['search'] ?? null;
    
    if ($id) {
        // جلب عميل واحد
        $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
        $stmt->execute([$id]);
        $customer = $stmt->fetch();
        
        if ($customer) {
            sendSuccess($customer);
        } else {
            sendError('العميل غير موجود', 404);
        }
    } else {
        // جلب جميع العملاء
        $limit = $_GET['limit'] ?? 100;
        $offset = $_GET['offset'] ?? 0;
        
        $sql = "SELECT * FROM customers WHERE 1=1";
        $params = [];
        
        if ($type) {
            $sql .= " AND type = ?";
            $params[] = $type;
        }
        
        if ($search) {
            $sql .= " AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $sql .= " ORDER BY name ASC LIMIT ? OFFSET ?";
        $params[] = (int)$limit;
        $params[] = (int)$offset;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $customers = $stmt->fetchAll();
        
        // حساب العدد الإجمالي
        $countSql = "SELECT COUNT(*) as total FROM customers WHERE 1=1";
        $countParams = [];
        
        if ($type) {
            $countSql .= " AND type = ?";
            $countParams[] = $type;
        }
        
        if ($search) {
            $countSql .= " AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)";
            $countParams[] = "%$search%";
            $countParams[] = "%$search%";
            $countParams[] = "%$search%";
        }
        
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($countParams);
        $total = $countStmt->fetch()['total'];
        
        sendSuccess([
            'customers' => $customers,
            'total' => $total,
            'limit' => (int)$limit,
            'offset' => (int)$offset
        ]);
    }
}

// دالة إنشاء عميل جديد
function handleCreateCustomer($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendError('بيانات غير صحيحة');
    }
    
    $requiredFields = ['name', 'type'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            sendError("الحقل $field مطلوب");
        }
    }
    
    if (!in_array($input['type'], ['customer', 'supplier'])) {
        sendError('نوع العميل يجب أن يكون customer أو supplier');
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO customers (name, type, phone, email, address, balance) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $input['name'],
            $input['type'],
            $input['phone'] ?? '',
            $input['email'] ?? '',
            $input['address'] ?? '',
            $input['balance'] ?? 0.00
        ]);
        
        $customerId = $pdo->lastInsertId();
        
        // تحديث الإحصائيات إذا كان العميل موجود في الأعمال
        updateCustomerStatsFromJobs($pdo, $input['name']);
        
        // تسجيل النشاط
        logActivity('create_customer', ['customer_id' => $customerId, 'name' => $input['name'], 'type' => $input['type']]);
        
        sendSuccess(['id' => $customerId], 'تم إنشاء العميل بنجاح');
        
    } catch (PDOException $e) {
        if ($e->getCode() == 23000) { // Duplicate entry
            sendError('اسم العميل موجود مسبقاً');
        } else {
            error_log("Create customer error: " . $e->getMessage());
            sendError('فشل في إنشاء العميل', 500);
        }
    }
}

// دالة تحديث عميل
function handleUpdateCustomer($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    $id = $_GET['id'] ?? $input['id'] ?? null;
    
    if (!$id) {
        sendError('معرف العميل مطلوب');
    }
    
    if (!$input) {
        sendError('بيانات غير صحيحة');
    }
    
    // التحقق من وجود العميل
    $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
    $stmt->execute([$id]);
    $existingCustomer = $stmt->fetch();
    
    if (!$existingCustomer) {
        sendError('العميل غير موجود', 404);
    }
    
    try {
        $updateFields = [];
        $params = [];
        
        $allowedFields = ['name', 'type', 'phone', 'email', 'address', 'balance'];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                if ($field === 'type' && !in_array($input[$field], ['customer', 'supplier'])) {
                    sendError('نوع العميل يجب أن يكون customer أو supplier');
                }
                $updateFields[] = "$field = ?";
                $params[] = $input[$field];
            }
        }
        
        if (empty($updateFields)) {
            sendError('لا توجد حقول للتحديث');
        }
        
        $params[] = $id;
        
        $sql = "UPDATE customers SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        // إذا تم تغيير الاسم، تحديث الأعمال المرتبطة
        if (isset($input['name']) && $input['name'] !== $existingCustomer['name']) {
            $stmt = $pdo->prepare("UPDATE jobs SET customer_name = ? WHERE customer_name = ?");
            $stmt->execute([$input['name'], $existingCustomer['name']]);
        }
        
        // تسجيل النشاط
        logActivity('update_customer', ['customer_id' => $id, 'changes' => $input]);
        
        sendSuccess(null, 'تم تحديث العميل بنجاح');
        
    } catch (PDOException $e) {
        if ($e->getCode() == 23000) { // Duplicate entry
            sendError('اسم العميل موجود مسبقاً');
        } else {
            error_log("Update customer error: " . $e->getMessage());
            sendError('فشل في تحديث العميل', 500);
        }
    }
}

// دالة حذف عميل
function handleDeleteCustomer($pdo) {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        sendError('معرف العميل مطلوب');
    }
    
    // التحقق من وجود العميل
    $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
    $stmt->execute([$id]);
    $customer = $stmt->fetch();
    
    if (!$customer) {
        sendError('العميل غير موجود', 404);
    }
    
    // التحقق من وجود أعمال مرتبطة
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM jobs WHERE customer_name = ?");
    $stmt->execute([$customer['name']]);
    $jobCount = $stmt->fetch()['count'];
    
    if ($jobCount > 0) {
        sendError("لا يمكن حذف العميل لأنه مرتبط بـ $jobCount عمل");
    }
    
    try {
        $stmt = $pdo->prepare("DELETE FROM customers WHERE id = ?");
        $stmt->execute([$id]);
        
        // تسجيل النشاط
        logActivity('delete_customer', ['customer_id' => $id, 'name' => $customer['name']]);
        
        sendSuccess(null, 'تم حذف العميل بنجاح');
        
    } catch (PDOException $e) {
        error_log("Delete customer error: " . $e->getMessage());
        sendError('فشل في حذف العميل', 500);
    }
}

// دالة تحديث إحصائيات العميل من الأعمال
function updateCustomerStatsFromJobs($pdo, $customerName) {
    if (!$customerName) return;
    
    try {
        // حساب إحصائيات العميل من الأعمال
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_jobs,
                COALESCE(SUM(amount), 0) as total_amount
            FROM jobs 
            WHERE customer_name = ?
        ");
        $stmt->execute([$customerName]);
        $stats = $stmt->fetch();
        
        // تحديث الإحصائيات
        $stmt = $pdo->prepare("
            UPDATE customers 
            SET total_jobs = ?, total_amount = ?, updated_at = NOW()
            WHERE name = ?
        ");
        $stmt->execute([$stats['total_jobs'], $stats['total_amount'], $customerName]);
        
    } catch (PDOException $e) {
        error_log("Update customer stats from jobs error: " . $e->getMessage());
    }
}
?>