/**
 * عميل API للاتصال بقاعدة البيانات المشتركة
 * يدعم الشبكة المحلية والمزامنة الفورية
 */

class PrintingSystemAPI {
    constructor(baseUrl = '', apiKey = 'printing_system_2024') {
        this.baseUrl = baseUrl || this.detectServerUrl();
        this.apiKey = apiKey;
        this.isOnline = false;
        this.syncQueue = [];
        this.eventListeners = {};
        
        this.init();
    }

    // تهيئة النظام
    async init() {
        await this.checkConnection();
        this.setupEventListeners();
        this.startSyncProcess();
        
        console.log('🌐 نظام API جاهز - الخادم:', this.baseUrl);
    }

    // اكتشاف عنوان الخادم تلقائياً
    detectServerUrl() {
        const hostname = window.location.hostname;
        const port = window.location.port || '80';
        
        // إذا كان يعمل محلياً
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return `http://${hostname}:${port}`;
        }
        
        // إذا كان على الشبكة المحلية
        return `http://${hostname}:${port}`;
    }

    // فحص الاتصال بالخادم
    async checkConnection() {
        try {
            const response = await fetch(`${this.baseUrl}/api/health.php`, {
                method: 'GET',
                headers: this.getHeaders()
            });
            
            this.isOnline = response.ok;
            this.emit('connectionChange', this.isOnline);
            
            return this.isOnline;
        } catch (error) {
            this.isOnline = false;
            this.emit('connectionChange', false);
            console.warn('⚠️ فقدان الاتصال بالخادم:', error.message);
            return false;
        }
    }

    // الحصول على headers الطلب
    getHeaders() {
        return {
            'Content-Type': 'application/json',
            'X-API-Key': this.apiKey,
            'Accept': 'application/json'
        };
    }

    // إرسال طلب HTTP
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}/api/${endpoint}`;
        const config = {
            headers: this.getHeaders(),
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error(`❌ خطأ في API ${endpoint}:`, error.message);
            
            // إضافة للطابور إذا كان الخادم غير متاح
            if (!this.isOnline && options.method !== 'GET') {
                this.addToSyncQueue(endpoint, options);
            }
            
            throw error;
        }
    }

    // === إدارة الأعمال ===
    
    // جلب جميع الأعمال
    async getJobs(filters = {}) {
        const params = new URLSearchParams(filters);
        return await this.request(`jobs.php?${params}`);
    }

    // جلب عمل واحد
    async getJob(id) {
        return await this.request(`jobs.php?id=${id}`);
    }

    // إنشاء عمل جديد
    async createJob(jobData) {
        return await this.request('jobs.php', {
            method: 'POST',
            body: JSON.stringify(jobData)
        });
    }

    // تحديث عمل
    async updateJob(id, jobData) {
        return await this.request(`jobs.php?id=${id}`, {
            method: 'PUT',
            body: JSON.stringify(jobData)
        });
    }

    // حذف عمل
    async deleteJob(id) {
        return await this.request(`jobs.php?id=${id}`, {
            method: 'DELETE'
        });
    }

    // === إدارة العملاء ===
    
    // جلب جميع العملاء
    async getCustomers(filters = {}) {
        const params = new URLSearchParams(filters);
        return await this.request(`customers.php?${params}`);
    }

    // جلب عميل واحد
    async getCustomer(id) {
        return await this.request(`customers.php?id=${id}`);
    }

    // إنشاء عميل جديد
    async createCustomer(customerData) {
        return await this.request('customers.php', {
            method: 'POST',
            body: JSON.stringify(customerData)
        });
    }

    // تحديث عميل
    async updateCustomer(id, customerData) {
        return await this.request(`customers.php?id=${id}`, {
            method: 'PUT',
            body: JSON.stringify(customerData)
        });
    }

    // حذف عميل
    async deleteCustomer(id) {
        return await this.request(`customers.php?id=${id}`, {
            method: 'DELETE'
        });
    }

    // === إدارة الإيصالات ===
    
    // جلب جميع الإيصالات
    async getReceipts(filters = {}) {
        const params = new URLSearchParams(filters);
        return await this.request(`receipts.php?${params}`);
    }

    // إنشاء إيصال جديد
    async createReceipt(receiptData) {
        return await this.request('receipts.php', {
            method: 'POST',
            body: JSON.stringify(receiptData)
        });
    }

    // تحديث إيصال
    async updateReceipt(id, receiptData) {
        return await this.request(`receipts.php?id=${id}`, {
            method: 'PUT',
            body: JSON.stringify(receiptData)
        });
    }

    // حذف إيصال
    async deleteReceipt(id) {
        return await this.request(`receipts.php?id=${id}`, {
            method: 'DELETE'
        });
    }

    // === المزامنة والطابور ===
    
    // إضافة عملية للطابور
    addToSyncQueue(endpoint, options) {
        this.syncQueue.push({
            endpoint,
            options,
            timestamp: Date.now(),
            id: Math.random().toString(36).substr(2, 9)
        });
        
        this.saveSyncQueue();
        console.log('📝 تم إضافة عملية للطابور:', endpoint);
    }

    // بدء عملية المزامنة
    startSyncProcess() {
        // فحص الاتصال كل 30 ثانية
        setInterval(async () => {
            await this.checkConnection();
            
            if (this.isOnline && this.syncQueue.length > 0) {
                await this.processSyncQueue();
            }
        }, 30000);

        // تحميل الطابور المحفوظ
        this.loadSyncQueue();
    }

    // معالجة طابور المزامنة
    async processSyncQueue() {
        console.log(`🔄 بدء مزامنة ${this.syncQueue.length} عملية...`);
        
        const processedItems = [];
        
        for (const item of this.syncQueue) {
            try {
                await this.request(item.endpoint, item.options);
                processedItems.push(item.id);
                console.log('✅ تم مزامنة:', item.endpoint);
            } catch (error) {
                console.error('❌ فشل في مزامنة:', item.endpoint, error.message);
                
                // إزالة العمليات القديمة (أكثر من ساعة)
                if (Date.now() - item.timestamp > 3600000) {
                    processedItems.push(item.id);
                }
            }
        }

        // إزالة العمليات المعالجة
        this.syncQueue = this.syncQueue.filter(item => !processedItems.includes(item.id));
        this.saveSyncQueue();
        
        if (processedItems.length > 0) {
            this.emit('syncComplete', processedItems.length);
        }
    }

    // حفظ طابور المزامنة
    saveSyncQueue() {
        try {
            localStorage.setItem('printing_sync_queue', JSON.stringify(this.syncQueue));
        } catch (error) {
            console.error('فشل في حفظ طابور المزامنة:', error);
        }
    }

    // تحميل طابور المزامنة
    loadSyncQueue() {
        try {
            const saved = localStorage.getItem('printing_sync_queue');
            if (saved) {
                this.syncQueue = JSON.parse(saved);
                console.log(`📂 تم تحميل ${this.syncQueue.length} عملية من الطابور`);
            }
        } catch (error) {
            console.error('فشل في تحميل طابور المزامنة:', error);
            this.syncQueue = [];
        }
    }

    // === إدارة الأحداث ===
    
    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // مراقبة حالة الاتصال
        window.addEventListener('online', () => {
            console.log('🌐 تم استعادة الاتصال بالإنترنت');
            this.checkConnection();
        });

        window.addEventListener('offline', () => {
            console.log('📴 فقدان الاتصال بالإنترنت');
            this.isOnline = false;
            this.emit('connectionChange', false);
        });
    }

    // إضافة مستمع حدث
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(callback);
    }

    // إزالة مستمع حدث
    off(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
        }
    }

    // إطلاق حدث
    emit(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`خطأ في معالج الحدث ${event}:`, error);
                }
            });
        }
    }

    // === وظائف مساعدة ===
    
    // الحصول على حالة الاتصال
    isConnected() {
        return this.isOnline;
    }

    // الحصول على عدد العمليات في الطابور
    getPendingOperations() {
        return this.syncQueue.length;
    }

    // مسح طابور المزامنة
    clearSyncQueue() {
        this.syncQueue = [];
        this.saveSyncQueue();
        console.log('🗑️ تم مسح طابور المزامنة');
    }

    // إجبار المزامنة
    async forcSync() {
        if (this.isOnline) {
            await this.processSyncQueue();
        } else {
            throw new Error('لا يوجد اتصال بالخادم');
        }
    }
}

// إنشاء مثيل عام للـ API
window.printingAPI = new PrintingSystemAPI();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PrintingSystemAPI;
}