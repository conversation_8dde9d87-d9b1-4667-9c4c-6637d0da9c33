<?php
require_once '../config.php';

// التحقق من API Key
validateApiKey();

// الحصول على طريقة الطلب
$method = $_SERVER['REQUEST_METHOD'];

// معالجة طلبات OPTIONS للـ CORS
if ($method === 'OPTIONS') {
    sendResponse(['message' => 'OK']);
}

try {
    $pdo = getDBConnection();
    if (!$pdo) {
        sendError('فشل الاتصال بقاعدة البيانات', 500);
    }

    switch ($method) {
        case 'GET':
            handleGetReceipts($pdo);
            break;
        case 'POST':
            handleCreateReceipt($pdo);
            break;
        case 'PUT':
            handleUpdateReceipt($pdo);
            break;
        case 'DELETE':
            handleDeleteReceipt($pdo);
            break;
        default:
            sendError('طريقة الطلب غير مدعومة', 405);
    }
} catch (Exception $e) {
    error_log("Receipts API Error: " . $e->getMessage());
    sendError('خطأ في الخادم', 500);
}

// دالة جلب الإيصالات
function handleGetReceipts($pdo) {
    $id = $_GET['id'] ?? null;
    $customer = $_GET['customer'] ?? null;
    $dateFrom = $_GET['date_from'] ?? null;
    $dateTo = $_GET['date_to'] ?? null;
    
    if ($id) {
        // جلب إيصال واحد
        $stmt = $pdo->prepare("SELECT * FROM receipts WHERE id = ?");
        $stmt->execute([$id]);
        $receipt = $stmt->fetch();
        
        if ($receipt) {
            sendSuccess($receipt);
        } else {
            sendError('الإيصال غير موجود', 404);
        }
    } else {
        // جلب جميع الإيصالات
        $limit = $_GET['limit'] ?? 100;
        $offset = $_GET['offset'] ?? 0;
        
        $sql = "SELECT * FROM receipts WHERE 1=1";
        $params = [];
        
        if ($customer) {
            $sql .= " AND customer_name LIKE ?";
            $params[] = "%$customer%";
        }
        
        if ($dateFrom) {
            $sql .= " AND DATE(created_at) >= ?";
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $sql .= " AND DATE(created_at) <= ?";
            $params[] = $dateTo;
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = (int)$limit;
        $params[] = (int)$offset;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $receipts = $stmt->fetchAll();
        
        // حساب العدد الإجمالي
        $countSql = "SELECT COUNT(*) as total FROM receipts WHERE 1=1";
        $countParams = [];
        
        if ($customer) {
            $countSql .= " AND customer_name LIKE ?";
            $countParams[] = "%$customer%";
        }
        
        if ($dateFrom) {
            $countSql .= " AND DATE(created_at) >= ?";
            $countParams[] = $dateFrom;
        }
        
        if ($dateTo) {
            $countSql .= " AND DATE(created_at) <= ?";
            $countParams[] = $dateTo;
        }
        
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($countParams);
        $total = $countStmt->fetch()['total'];
        
        sendSuccess([
            'receipts' => $receipts,
            'total' => $total,
            'limit' => (int)$limit,
            'offset' => (int)$offset
        ]);
    }
}

// دالة إنشاء إيصال جديد
function handleCreateReceipt($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendError('بيانات غير صحيحة');
    }
    
    $requiredFields = ['customer_name', 'amount'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            sendError("الحقل $field مطلوب");
        }
    }
    
    if ($input['amount'] <= 0) {
        sendError('المبلغ يجب أن يكون أكبر من صفر');
    }
    
    try {
        // إنشاء رقم إيصال تلقائي
        $receiptNumber = generateReceiptNumber($pdo);
        
        $stmt = $pdo->prepare("
            INSERT INTO receipts (receipt_number, customer_name, amount, payment_method, description) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $receiptNumber,
            $input['customer_name'],
            $input['amount'],
            $input['payment_method'] ?? 'cash',
            $input['description'] ?? ''
        ]);
        
        $receiptId = $pdo->lastInsertId();
        
        // تحديث رصيد العميل
        updateCustomerBalance($pdo, $input['customer_name'], -$input['amount']);
        
        // تسجيل النشاط
        logActivity('create_receipt', [
            'receipt_id' => $receiptId, 
            'receipt_number' => $receiptNumber,
            'customer_name' => $input['customer_name'],
            'amount' => $input['amount']
        ]);
        
        sendSuccess([
            'id' => $receiptId,
            'receipt_number' => $receiptNumber
        ], 'تم إنشاء الإيصال بنجاح');
        
    } catch (PDOException $e) {
        error_log("Create receipt error: " . $e->getMessage());
        sendError('فشل في إنشاء الإيصال', 500);
    }
}

// دالة تحديث إيصال
function handleUpdateReceipt($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    $id = $_GET['id'] ?? $input['id'] ?? null;
    
    if (!$id) {
        sendError('معرف الإيصال مطلوب');
    }
    
    if (!$input) {
        sendError('بيانات غير صحيحة');
    }
    
    // التحقق من وجود الإيصال
    $stmt = $pdo->prepare("SELECT * FROM receipts WHERE id = ?");
    $stmt->execute([$id]);
    $existingReceipt = $stmt->fetch();
    
    if (!$existingReceipt) {
        sendError('الإيصال غير موجود', 404);
    }
    
    try {
        $updateFields = [];
        $params = [];
        
        $allowedFields = ['customer_name', 'amount', 'payment_method', 'description'];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                if ($field === 'amount' && $input[$field] <= 0) {
                    sendError('المبلغ يجب أن يكون أكبر من صفر');
                }
                $updateFields[] = "$field = ?";
                $params[] = $input[$field];
            }
        }
        
        if (empty($updateFields)) {
            sendError('لا توجد حقول للتحديث');
        }
        
        $params[] = $id;
        
        $sql = "UPDATE receipts SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        // تحديث أرصدة العملاء إذا تغير المبلغ أو العميل
        if (isset($input['amount']) || isset($input['customer_name'])) {
            // إلغاء التأثير السابق
            updateCustomerBalance($pdo, $existingReceipt['customer_name'], $existingReceipt['amount']);
            
            // تطبيق التأثير الجديد
            $newCustomer = $input['customer_name'] ?? $existingReceipt['customer_name'];
            $newAmount = $input['amount'] ?? $existingReceipt['amount'];
            updateCustomerBalance($pdo, $newCustomer, -$newAmount);
        }
        
        // تسجيل النشاط
        logActivity('update_receipt', ['receipt_id' => $id, 'changes' => $input]);
        
        sendSuccess(null, 'تم تحديث الإيصال بنجاح');
        
    } catch (PDOException $e) {
        error_log("Update receipt error: " . $e->getMessage());
        sendError('فشل في تحديث الإيصال', 500);
    }
}

// دالة حذف إيصال
function handleDeleteReceipt($pdo) {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        sendError('معرف الإيصال مطلوب');
    }
    
    // التحقق من وجود الإيصال
    $stmt = $pdo->prepare("SELECT * FROM receipts WHERE id = ?");
    $stmt->execute([$id]);
    $receipt = $stmt->fetch();
    
    if (!$receipt) {
        sendError('الإيصال غير موجود', 404);
    }
    
    try {
        $stmt = $pdo->prepare("DELETE FROM receipts WHERE id = ?");
        $stmt->execute([$id]);
        
        // إلغاء تأثير الإيصال على رصيد العميل
        updateCustomerBalance($pdo, $receipt['customer_name'], $receipt['amount']);
        
        // تسجيل النشاط
        logActivity('delete_receipt', [
            'receipt_id' => $id, 
            'receipt_number' => $receipt['receipt_number'],
            'customer_name' => $receipt['customer_name'],
            'amount' => $receipt['amount']
        ]);
        
        sendSuccess(null, 'تم حذف الإيصال بنجاح');
        
    } catch (PDOException $e) {
        error_log("Delete receipt error: " . $e->getMessage());
        sendError('فشل في حذف الإيصال', 500);
    }
}

// دالة إنشاء رقم إيصال تلقائي
function generateReceiptNumber($pdo) {
    $prefix = 'REC';
    $year = date('Y');
    $month = date('m');
    
    // البحث عن آخر رقم إيصال في الشهر الحالي
    $stmt = $pdo->prepare("
        SELECT receipt_number 
        FROM receipts 
        WHERE receipt_number LIKE ? 
        ORDER BY receipt_number DESC 
        LIMIT 1
    ");
    $stmt->execute(["$prefix-$year$month-%"]);
    $lastReceipt = $stmt->fetch();
    
    if ($lastReceipt) {
        // استخراج الرقم التسلسلي وزيادته
        $parts = explode('-', $lastReceipt['receipt_number']);
        $sequence = intval(end($parts)) + 1;
    } else {
        $sequence = 1;
    }
    
    return sprintf("%s-%s%s-%04d", $prefix, $year, $month, $sequence);
}

// دالة تحديث رصيد العميل
function updateCustomerBalance($pdo, $customerName, $amount) {
    if (!$customerName || $amount == 0) return;
    
    try {
        // إنشاء العميل إذا لم يكن موجوداً
        $stmt = $pdo->prepare("
            INSERT INTO customers (name, type, balance) 
            VALUES (?, 'customer', 0)
            ON DUPLICATE KEY UPDATE name = name
        ");
        $stmt->execute([$customerName]);
        
        // تحديث الرصيد
        $stmt = $pdo->prepare("
            UPDATE customers 
            SET balance = balance + ?, updated_at = NOW()
            WHERE name = ?
        ");
        $stmt->execute([$amount, $customerName]);
        
    } catch (PDOException $e) {
        error_log("Update customer balance error: " . $e->getMessage());
    }
}
?>