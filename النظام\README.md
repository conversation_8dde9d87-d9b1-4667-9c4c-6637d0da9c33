# 🖨️ نظام إدارة الطباعة - الشبكة المحلية

نظام متكامل لإدارة الطباعة يدعم العمل على الشبكة المحلية مع قاعدة بيانات مشتركة.

## 🌟 المميزات

### 🔗 **الشبكة المحلية**
- **قاعدة بيانات مشتركة** على خادم محلي
- **مزامنة فورية** بين جميع الأجهزة
- **عمل بدون إنترنت** - شبكة محلية فقط
- **سرعة عالية** في نقل البيانات

### 📱 **دعم متعدد الأجهزة**
- **أجهزة الكمبيوتر** (Windows, Mac, Linux)
- **الهواتف والتابلت** (Android, iOS)
- **واجهة متجاوبة** تعمل على جميع الشاشات

### 🔄 **المزامنة التلقائية**
- **تحديث فوري** عند إضافة أعمال جديدة
- **مزامنة العملاء** بين النظامين
- **نسخ احتياطية تلقائية**

## 🛠️ متطلبات التشغيل

### الخادم (Server)
- **نظام التشغيل:** Windows/Linux/Mac
- **خادم ويب:** Apache أو Nginx
- **PHP:** الإصدار 7.4 أو أحدث
- **قاعدة البيانات:** MySQL 5.7 أو أحدث
- **الذاكرة:** 2GB RAM كحد أدنى
- **التخزين:** 10GB مساحة فارغة

### العملاء (Clients)
- **متصفح حديث:** Chrome, Firefox, Safari, Edge
- **اتصال بالشبكة المحلية**
- **JavaScript مفعل**

## 📋 خطوات التثبيت

### 1️⃣ **إعداد الخادم**

#### تثبيت XAMPP (الطريقة السهلة)
```bash
# تحميل XAMPP من الموقع الرسمي
https://www.apachefriends.org/download.html

# تشغيل Apache و MySQL
```

#### أو تثبيت منفصل
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install apache2 mysql-server php php-mysql

# CentOS/RHEL
sudo yum install httpd mariadb-server php php-mysql

# Windows
# تحميل وتثبيت Apache, MySQL, PHP منفصلة
```

### 2️⃣ **رفع ملفات النظام**

```bash
# نسخ ملفات النظام إلى مجلد الخادم
# XAMPP
cp -r النظام/* /opt/lampp/htdocs/printing/

# Apache العادي
cp -r النظام/* /var/www/html/printing/

# Windows XAMPP
# نسخ المجلد إلى C:\xampp\htdocs\printing\
```

### 3️⃣ **تشغيل التثبيت**

1. **افتح المتصفح** واذهب إلى:
   ```
   http://localhost/printing/install.php
   ```

2. **أدخل بيانات قاعدة البيانات:**
   - الخادم: `localhost`
   - اسم قاعدة البيانات: `printing_system`
   - المستخدم: `root`
   - كلمة المرور: (اتركها فارغة في XAMPP)

3. **أدخل بيانات الشركة:**
   - اسم المطبعة
   - رقم الهاتف
   - العنوان

4. **انقر "بدء التثبيت"**

### 4️⃣ **الوصول من الأجهزة الأخرى**

1. **اعرف عنوان IP للخادم:**
   ```bash
   # Windows
   ipconfig
   
   # Linux/Mac
   ifconfig
   ```

2. **من الأجهزة الأخرى، افتح:**
   ```
   http://[IP_ADDRESS]/printing/
   # مثال: http://*************/printing/
   ```

## 🔧 الإعدادات المتقدمة

### تخصيص عنوان IP
```php
// في ملف config.php
define('SERVER_IP', '*************');
define('SERVER_PORT', '80');
```

### إعدادات الأمان
```php
// تغيير مفتاح API
define('API_KEY', 'your_secure_key_here');

// تحديد الشبكات المسموحة
define('ALLOWED_NETWORKS', ['***********/24', '10.0.0.0/8']);
```

### النسخ الاحتياطي التلقائي
```bash
# إضافة مهمة cron للنسخ الاحتياطي اليومي
0 2 * * * /usr/bin/php /var/www/html/printing/backup.php
```

## 📱 استخدام النظام

### من الكمبيوتر
1. افتح المتصفح
2. اذهب إلى عنوان الخادم
3. ابدأ العمل فوراً

### من الهاتف
1. افتح المتصفح في الهاتف
2. اذهب إلى نفس العنوان
3. الواجهة ستتكيف تلقائياً مع الشاشة

### إضافة عمل جديد
1. انقر "إضافة عمل جديد"
2. املأ البيانات المطلوبة
3. اختر العميل أو أضف عميل جديد
4. احفظ - سيظهر فوراً على جميع الأجهزة

## 🔍 استكشاف الأخطاء

### مشكلة الاتصال بقاعدة البيانات
```bash
# فحص حالة MySQL
sudo systemctl status mysql

# إعادة تشغيل MySQL
sudo systemctl restart mysql
```

### مشكلة الصلاحيات
```bash
# إعطاء صلاحيات للمجلد
sudo chown -R www-data:www-data /var/www/html/printing/
sudo chmod -R 755 /var/www/html/printing/
```

### مشكلة الوصول من الأجهزة الأخرى
```bash
# فحص الجدار الناري
sudo ufw status

# السماح بالمنفذ 80
sudo ufw allow 80
```

## 📊 مراقبة النظام

### فحص حالة النظام
```
http://[SERVER_IP]/printing/api/health.php
```

### عرض السجلات
```bash
# سجلات Apache
tail -f /var/log/apache2/access.log

# سجلات PHP
tail -f /var/log/apache2/error.log
```

## 🔄 التحديثات

### تحديث النظام
1. احتفظ بنسخة احتياطية من قاعدة البيانات
2. نزل الإصدار الجديد
3. استبدل الملفات (احتفظ بـ config.php)
4. شغل سكريبت التحديث إذا وجد

### النسخ الاحتياطي اليدوي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u root -p printing_system > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات
tar -czf printing_backup_$(date +%Y%m%d).tar.gz /var/www/html/printing/
```

## 📞 الدعم الفني

### المشاكل الشائعة
- **"لا يمكن الاتصال بالخادم"**: تأكد من تشغيل Apache و MySQL
- **"خطأ في قاعدة البيانات"**: تأكد من صحة بيانات الاتصال
- **"لا يظهر على الأجهزة الأخرى"**: تأكد من عنوان IP والجدار الناري

### معلومات النظام
- **الإصدار:** 2.0
- **تاريخ الإصدار:** 2024
- **المطور:** نظام إدارة الطباعة

---

🎉 **مبروك! النظام جاهز للعمل على الشبكة المحلية**

جميع الأجهزة المتصلة بنفس الشبكة يمكنها الآن الوصول للنظام والعمل على نفس البيانات في الوقت الفعلي!